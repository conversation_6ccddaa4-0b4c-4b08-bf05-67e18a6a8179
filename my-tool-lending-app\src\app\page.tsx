import Link from 'next/link';
import RemoveBtn from '@/components/RemoveBtn';
import { HiPencilAlt } from 'react-icons/hi';

const getTools = async () => {
  try {
    const res = await fetch('http://localhost:3000/api/tools', {
      cache: 'no-store',
    });
    if (!res.ok) {
      throw new Error("Failed to fetch tools");
    }
    return res.json();
  } catch (error) {
    console.log("Error loading tools: ", error);
    return { tools: [] }; // Return empty array on error
  }
};

export default async function Home() {
  const data = await getTools();
  const tools = data?.tools || [];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-8">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <h1 className="text-5xl font-bold text-gray-800 mb-4">
            Community Tools
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Share tools, build community. Discover what your neighbors have to offer and contribute your own tools to help others.
          </p>
        </div>

        {tools && tools.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {tools.map((tool: any) => (
              <div key={tool._id} className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 overflow-hidden">
                {/* Tool Header */}
                <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-4">
                  <h2 className="text-xl font-bold text-white truncate">{tool.name}</h2>
                  <div className="flex items-center mt-2">
                    <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                      tool.status === 'available'
                        ? 'bg-green-100 text-green-800'
                        : tool.status === 'borrowed'
                        ? 'bg-yellow-100 text-yellow-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {tool.status?.charAt(0).toUpperCase() + tool.status?.slice(1) || 'Available'}
                    </span>
                  </div>
                </div>

                {/* Tool Content */}
                <div className="p-6">
                  <p className="text-gray-600 text-sm leading-relaxed mb-4 line-clamp-3">
                    {tool.description}
                  </p>

                  {/* Action Buttons */}
                  <div className="flex justify-between items-center pt-4 border-t border-gray-100">
                    <div className="flex gap-3">
                      <Link
                        href={`/edit-tool/${tool._id}`}
                        className="flex items-center justify-center w-10 h-10 bg-blue-100 text-blue-600 rounded-full hover:bg-blue-200 transition-colors duration-200"
                        title="Edit Tool"
                      >
                        <HiPencilAlt size={18} />
                      </Link>
                      <div className="flex items-center justify-center w-10 h-10 bg-red-100 text-red-600 rounded-full hover:bg-red-200 transition-colors duration-200">
                        <RemoveBtn id={tool._id} />
                      </div>
                    </div>
                    <div className="text-xs text-gray-400">
                      {tool.createdAt ? new Date(tool.createdAt).toLocaleDateString() : 'Recently added'}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-16">
            <div className="bg-white rounded-2xl shadow-lg p-12 max-w-md mx-auto">
              <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
              </div>
              <h3 className="text-2xl font-bold text-gray-800 mb-4">No tools available yet</h3>
              <p className="text-gray-600 mb-6">Be the first to add a tool to the community!</p>
              <Link
                href="/add-tool"
                className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all duration-200 transform hover:scale-105"
              >
                Add Your First Tool
              </Link>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
