import Link from 'next/link';
import RemoveBtn from '@/components/RemoveBtn';
import { HiPencilAlt } from 'react-icons/hi';

const getTools = async () => {
  try {
    const res = await fetch('http://localhost:3000/api/tools', {
      cache: 'no-store',
    });
    if (!res.ok) {
      throw new Error("Failed to fetch tools");
    }
    return res.json();
  } catch (error) {
    console.log("Error loading tools: ", error);
    return { tools: [] }; // Return empty array on error
  }
};

export default async function Home() {
  const data = await getTools();
  const tools = data?.tools || [];

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-3xl font-bold mb-6">Community Tools</h1>

      {tools && tools.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {tools.map((tool: any) => (
            <div key={tool._id} className="p-4 border rounded-lg shadow flex flex-col justify-between">
              <div>
                <h2 className="text-xl font-semibold">{tool.name}</h2>
                <p className="text-gray-600 mt-2">{tool.description}</p>
                <p className="text-sm text-gray-500 mt-2">Status: {tool.status}</p>
              </div>
              <div className="flex gap-2 mt-4">
                <RemoveBtn id={tool._id} />
                <Link href={`/edit-tool/${tool._id}`} className="text-blue-400 hover:text-blue-600">
                  <HiPencilAlt size={24} />
                </Link>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-8">
          <p className="text-gray-500 text-lg">No tools available yet.</p>
          <p className="text-gray-400">Be the first to add a tool to the community!</p>
        </div>
      )}
    </div>
  );
}
