'use client';

import { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import RemoveBtn from '@/components/RemoveBtn';
import SearchAndFilter from '@/components/SearchAndFilter';
import { Hi<PERSON><PERSON>cilAlt, HiLocationMarker, <PERSON><PERSON>ser, <PERSON><PERSON>lock, HiCurrencyDollar } from 'react-icons/hi';

interface Tool {
  _id: string;
  name: string;
  description: string;
  category?: string;
  condition?: string;
  location?: {
    city: string;
    area: string;
    postalCode?: string;
  };
  owner?: {
    name: string;
    email: string;
    phone?: string;
  };
  availability?: string;
  status?: string; // For backward compatibility
  borrowingTerms?: {
    maxDuration: number;
    deposit: number;
    instructions?: string;
  };
  tags?: string[];
  createdAt: string;
  updatedAt: string;
}

interface SearchParams {
  query: string;
  category: string;
  city: string;
  availability: string;
  condition: string;
  sortBy: string;
  sortOrder: string;
}

export default function Home() {
  const [tools, setTools] = useState<Tool[]>([]);
  const [filterOptions, setFilterOptions] = useState<any>({});
  const [isLoading, setIsLoading] = useState(false);
  const [totalResults, setTotalResults] = useState(0);

  // Search function
  const handleSearch = useCallback(async (searchParams: SearchParams) => {
    setIsLoading(true);
    try {
      const queryParams = new URLSearchParams();
      Object.entries(searchParams).forEach(([key, value]) => {
        if (value && value !== 'all') {
          queryParams.append(key, value);
        }
      });

      const response = await fetch(`/api/tools/search?${queryParams.toString()}`);
      const data = await response.json();

      if (response.ok) {
        setTools(data.tools || []);
        setFilterOptions(data.filterOptions || {});
        setTotalResults(data.totalResults || 0);
      } else {
        console.error('Search failed:', data.error);
        setTools([]);
        setTotalResults(0);
      }
    } catch (error) {
      console.error('Search error:', error);
      setTools([]);
      setTotalResults(0);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Load initial data
  useEffect(() => {
    handleSearch({
      query: '',
      category: 'all',
      city: '',
      availability: 'all',
      condition: 'all',
      sortBy: 'createdAt',
      sortOrder: 'desc'
    });
  }, [handleSearch]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-8">
        {/* Hero Section */}
        <div className="text-center mb-8">
          <h1 className="text-5xl font-bold text-gray-800 mb-4">
            Community Tools
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Share tools, build community. Discover what your neighbors have to offer and contribute your own tools to help others.
          </p>
        </div>

        {/* Search and Filter */}
        <SearchAndFilter
          onSearch={handleSearch}
          filterOptions={filterOptions}
          isLoading={isLoading}
        />

        {/* Results Summary */}
        <div className="mb-6">
          <p className="text-gray-600">
            {isLoading ? 'Searching...' : `Found ${totalResults} tool${totalResults !== 1 ? 's' : ''}`}
          </p>
        </div>

        {tools && tools.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {tools.map((tool) => (
              <div key={tool._id} className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 overflow-hidden">
                {/* Tool Header */}
                <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-4">
                  <div className="flex justify-between items-start">
                    <h2 className="text-xl font-bold text-white truncate flex-1">{tool.name}</h2>
                    <span className="ml-2 px-2 py-1 bg-white bg-opacity-20 text-white text-xs rounded-full">
                      {tool.category}
                    </span>
                  </div>
                  <div className="flex items-center mt-2">
                    <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                      (tool.availability || tool.status) === 'available'
                        ? 'bg-green-100 text-green-800'
                        : (tool.availability || tool.status) === 'borrowed'
                        ? 'bg-yellow-100 text-yellow-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {(() => {
                        const status = tool.availability || tool.status || 'available';
                        return status.charAt(0).toUpperCase() + status.slice(1);
                      })()}
                    </span>
                  </div>
                </div>

                {/* Tool Content */}
                <div className="p-6">
                  <p className="text-gray-600 text-sm leading-relaxed mb-4 line-clamp-3">
                    {tool.description}
                  </p>

                  {/* Tool Details */}
                  <div className="space-y-2 mb-4 text-sm">
                    {tool.location && (
                      <div className="flex items-center text-gray-500">
                        <HiLocationMarker className="h-4 w-4 mr-2" />
                        <span>{tool.location.city}, {tool.location.area}</span>
                      </div>
                    )}

                    {tool.owner && (
                      <div className="flex items-center text-gray-500">
                        <HiUser className="h-4 w-4 mr-2" />
                        <span>{tool.owner.name}</span>
                      </div>
                    )}

                    {tool.condition && (
                      <div className="flex items-center text-gray-500">
                        <span className="text-xs bg-gray-100 px-2 py-1 rounded">
                          Condition: {tool.condition}
                        </span>
                      </div>
                    )}

                    {tool.borrowingTerms && (
                      <div className="flex items-center justify-between text-gray-500">
                        <div className="flex items-center">
                          <HiClock className="h-4 w-4 mr-1" />
                          <span className="text-xs">Max {tool.borrowingTerms.maxDuration} days</span>
                        </div>
                        {tool.borrowingTerms.deposit > 0 && (
                          <div className="flex items-center">
                            <HiCurrencyDollar className="h-4 w-4 mr-1" />
                            <span className="text-xs">LKR {tool.borrowingTerms.deposit}</span>
                          </div>
                        )}
                      </div>
                    )}
                  </div>

                  {/* Tags */}
                  {tool.tags && tool.tags.length > 0 && (
                    <div className="mb-4">
                      <div className="flex flex-wrap gap-1">
                        {tool.tags.slice(0, 3).map((tag, index) => (
                          <span key={index} className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                            {tag}
                          </span>
                        ))}
                        {tool.tags.length > 3 && (
                          <span className="text-xs text-gray-500">+{tool.tags.length - 3} more</span>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Action Buttons */}
                  <div className="flex justify-between items-center pt-4 border-t border-gray-100">
                    <div className="flex gap-3">
                      <Link
                        href={`/edit-tool/${tool._id}`}
                        className="flex items-center justify-center w-10 h-10 bg-blue-100 text-blue-600 rounded-full hover:bg-blue-200 transition-colors duration-200"
                        title="Edit Tool"
                      >
                        <HiPencilAlt size={18} />
                      </Link>
                      <div className="flex items-center justify-center w-10 h-10 bg-red-100 text-red-600 rounded-full hover:bg-red-200 transition-colors duration-200">
                        <RemoveBtn id={tool._id} />
                      </div>
                    </div>
                    <div className="text-xs text-gray-400">
                      {tool.createdAt ? new Date(tool.createdAt).toLocaleDateString() : 'Recently added'}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : !isLoading ? (
          <div className="text-center py-16">
            <div className="bg-white rounded-2xl shadow-lg p-12 max-w-md mx-auto">
              <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
              </div>
              <h3 className="text-2xl font-bold text-gray-800 mb-4">No tools found</h3>
              <p className="text-gray-600 mb-6">Try adjusting your search criteria or be the first to add a tool!</p>
              <Link
                href="/add-tool"
                className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all duration-200 transform hover:scale-105"
              >
                Add Your First Tool
              </Link>
            </div>
          </div>
        ) : null}
      </div>
    </div>
  );
}
