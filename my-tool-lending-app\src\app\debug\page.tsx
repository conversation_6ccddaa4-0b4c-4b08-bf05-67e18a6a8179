'use client';

import { useState, useEffect } from 'react';

export default function DebugPage() {
  const [tools, setTools] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [testName, setTestName] = useState('Test Tool');
  const [testDescription, setTestDescription] = useState('This is a test tool for debugging');

  const fetchTools = async () => {
    setLoading(true);
    setError('');
    try {
      const response = await fetch('/api/tools');
      const data = await response.json();
      
      if (response.ok) {
        setTools(data.tools || []);
      } else {
        setError(`Failed to fetch: ${data.error || 'Unknown error'}`);
      }
    } catch (err) {
      setError(`Network error: ${err}`);
    } finally {
      setLoading(false);
    }
  };

  const createTestTool = async () => {
    setLoading(true);
    setError('');
    try {
      const response = await fetch('/api/tools', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: testName,
          description: testDescription,
        }),
      });
      
      const data = await response.json();
      
      if (response.ok) {
        alert('Tool created successfully!');
        fetchTools(); // Refresh the list
      } else {
        setError(`Failed to create: ${data.error || 'Unknown error'}`);
      }
    } catch (err) {
      setError(`Network error: ${err}`);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTools();
  }, []);

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-3xl font-bold mb-6">Database Debug Page</h1>
      
      {/* Test Tool Creation */}
      <div className="bg-white p-6 rounded-lg shadow mb-6">
        <h2 className="text-xl font-semibold mb-4">Test Tool Creation</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <input
            type="text"
            value={testName}
            onChange={(e) => setTestName(e.target.value)}
            placeholder="Tool name"
            className="border p-2 rounded"
          />
          <input
            type="text"
            value={testDescription}
            onChange={(e) => setTestDescription(e.target.value)}
            placeholder="Tool description"
            className="border p-2 rounded"
          />
        </div>
        <button
          onClick={createTestTool}
          disabled={loading}
          className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50"
        >
          {loading ? 'Creating...' : 'Create Test Tool'}
        </button>
      </div>

      {/* Refresh Button */}
      <div className="mb-6">
        <button
          onClick={fetchTools}
          disabled={loading}
          className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 disabled:opacity-50"
        >
          {loading ? 'Loading...' : 'Refresh Tools'}
        </button>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
          <strong>Error:</strong> {error}
        </div>
      )}

      {/* Tools Display */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h2 className="text-xl font-semibold mb-4">Tools in Database ({tools.length})</h2>
        
        {loading ? (
          <p>Loading...</p>
        ) : tools.length > 0 ? (
          <div className="space-y-4">
            {tools.map((tool: any, index) => (
              <div key={tool._id || index} className="border p-4 rounded">
                <h3 className="font-semibold">{tool.name}</h3>
                <p className="text-gray-600">{tool.description}</p>
                <p className="text-sm text-gray-500">
                  Status: {tool.status} | ID: {tool._id}
                </p>
                {tool.createdAt && (
                  <p className="text-xs text-gray-400">
                    Created: {new Date(tool.createdAt).toLocaleString()}
                  </p>
                )}
              </div>
            ))}
          </div>
        ) : (
          <p className="text-gray-500">No tools found in database</p>
        )}
      </div>

      {/* Environment Info */}
      <div className="bg-gray-100 p-4 rounded mt-6">
        <h3 className="font-semibold mb-2">Environment Info</h3>
        <p className="text-sm">
          MongoDB URI: {process.env.MONGODB_URI ? 'Configured ✅' : 'Missing ❌'}
        </p>
      </div>
    </div>
  );
}
