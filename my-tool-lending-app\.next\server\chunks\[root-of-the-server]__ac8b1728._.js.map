{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Community%20Tool%20Lending%20Platform/my-tool-lending-app/src/lib/mongodb.ts"], "sourcesContent": ["import mongoose from 'mongoose';\r\n\r\n// Connection state tracking\r\nlet isConnected = false;\r\n\r\nconst connectMongoDB = async () => {\r\n  // If already connected, return early\r\n  if (isConnected) {\r\n    console.log(\"Already connected to MongoDB.\");\r\n    return;\r\n  }\r\n\r\n  try {\r\n    const mongoUri = process.env.MONGODB_URI;\r\n\r\n    if (!mongoUri) {\r\n      throw new Error(\"MONGODB_URI environment variable is not defined\");\r\n    }\r\n\r\n    console.log(\"Attempting to connect to MongoDB...\");\r\n\r\n    // Connect with additional options for better error handling\r\n    await mongoose.connect(mongoUri);\r\n\r\n    isConnected = true;\r\n    console.log(\"✅ Successfully connected to MongoDB.\");\r\n\r\n    // Log connection details (without sensitive info)\r\n    const connection = mongoose.connection;\r\n    console.log(`📊 Database: ${connection.db?.databaseName}`);\r\n    console.log(`🔗 Host: ${connection.host}:${connection.port}`);\r\n    console.log(`📈 Ready State: ${connection.readyState}`);\r\n\r\n  } catch (error) {\r\n    isConnected = false;\r\n    console.error(\"❌ Error connecting to MongoDB:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Function to check connection status\r\nexport const getConnectionStatus = () => {\r\n  return {\r\n    isConnected,\r\n    readyState: mongoose.connection.readyState,\r\n    host: mongoose.connection.host,\r\n    port: mongoose.connection.port,\r\n    databaseName: mongoose.connection.db?.databaseName,\r\n  };\r\n};\r\n\r\n// Function to disconnect\r\nexport const disconnectMongoDB = async () => {\r\n  try {\r\n    await mongoose.disconnect();\r\n    isConnected = false;\r\n    console.log(\"🔌 Disconnected from MongoDB.\");\r\n  } catch (error) {\r\n    console.error(\"❌ Error disconnecting from MongoDB:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport default connectMongoDB;"], "names": [], "mappings": ";;;;;AAAA;;AAEA,4BAA4B;AAC5B,IAAI,cAAc;AAElB,MAAM,iBAAiB;IACrB,qCAAqC;IACrC,IAAI,aAAa;QACf,QAAQ,GAAG,CAAC;QACZ;IACF;IAEA,IAAI;QACF,MAAM,WAAW,QAAQ,GAAG,CAAC,WAAW;QAExC,IAAI,CAAC,UAAU;YACb,MAAM,IAAI,MAAM;QAClB;QAEA,QAAQ,GAAG,CAAC;QAEZ,4DAA4D;QAC5D,MAAM,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC;QAEvB,cAAc;QACd,QAAQ,GAAG,CAAC;QAEZ,kDAAkD;QAClD,MAAM,aAAa,yGAAA,CAAA,UAAQ,CAAC,UAAU;QACtC,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,WAAW,EAAE,EAAE,cAAc;QACzD,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,WAAW,IAAI,CAAC,CAAC,EAAE,WAAW,IAAI,EAAE;QAC5D,QAAQ,GAAG,CAAC,CAAC,gBAAgB,EAAE,WAAW,UAAU,EAAE;IAExD,EAAE,OAAO,OAAO;QACd,cAAc;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAGO,MAAM,sBAAsB;IACjC,OAAO;QACL;QACA,YAAY,yGAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,UAAU;QAC1C,MAAM,yGAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,IAAI;QAC9B,MAAM,yGAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,IAAI;QAC9B,cAAc,yGAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,EAAE,EAAE;IACxC;AACF;AAGO,MAAM,oBAAoB;IAC/B,IAAI;QACF,MAAM,yGAAA,CAAA,UAAQ,CAAC,UAAU;QACzB,cAAc;QACd,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,MAAM;IACR;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 122, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Community%20Tool%20Lending%20Platform/my-tool-lending-app/src/models/tool.ts"], "sourcesContent": ["import mongoose, { Schema } from 'mongoose';\r\n\r\nconst toolSchema = new Schema(\r\n  {\r\n    name: String,\r\n    description: String,\r\n    status: {\r\n        type: String,\r\n        default: 'available',\r\n    }\r\n  },\r\n  {\r\n    timestamps: true, // This will automatically add createdAt and updatedAt fields\r\n  }\r\n);\r\n\r\nconst Tool = mongoose.models.Tool || mongoose.model(\"Tool\", toolSchema);\r\n\r\nexport default Tool;"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,aAAa,IAAI,yGAAA,CAAA,SAAM,CAC3B;IACE,MAAM;IACN,aAAa;IACb,QAAQ;QACJ,MAAM;QACN,SAAS;IACb;AACF,GACA;IACE,YAAY;AACd;AAGF,MAAM,OAAO,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAC,QAAQ;uCAE7C", "debugId": null}}, {"offset": {"line": 153, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Community%20Tool%20Lending%20Platform/my-tool-lending-app/src/app/api/tools/route.ts"], "sourcesContent": ["import connectMongoDB from \"@/lib/mongodb\";\r\nimport Tool from \"@/models/tool\";\r\nimport { NextResponse } from \"next/server\";\r\n\r\n// Function to CREATE a new tool\r\nexport async function POST(request: Request) {\r\n  const { name, description } = await request.json();\r\n  await connectMongoDB();\r\n  await Tool.create({ name, description });\r\n  return NextResponse.json({ message: \"Tool Created\" }, { status: 201 });\r\n}\r\n\r\n// Function to GET all tools\r\nexport async function GET() {\r\n  await connectMongoDB();\r\n  const tools = await Tool.find();\r\n  return NextResponse.json({ tools });\r\n}\r\n\r\n// Function to DELETE a tool\r\nexport async function DELETE(request: Request) {\r\n  const url = new URL(request.url);\r\n  const id = url.searchParams.get('id');\r\n  await connectMongoDB();\r\n  await Tool.findByIdAndDelete(id);\r\n  return NextResponse.json({ message: \"Tool deleted\" }, { status: 200 });\r\n}"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;AAGO,eAAe,KAAK,OAAgB;IACzC,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG,MAAM,QAAQ,IAAI;IAChD,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAc,AAAD;IACnB,MAAM,uHAAA,CAAA,UAAI,CAAC,MAAM,CAAC;QAAE;QAAM;IAAY;IACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QAAE,SAAS;IAAe,GAAG;QAAE,QAAQ;IAAI;AACtE;AAGO,eAAe;IACpB,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAc,AAAD;IACnB,MAAM,QAAQ,MAAM,uHAAA,CAAA,UAAI,CAAC,IAAI;IAC7B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QAAE;IAAM;AACnC;AAGO,eAAe,OAAO,OAAgB;IAC3C,MAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;IAC/B,MAAM,KAAK,IAAI,YAAY,CAAC,GAAG,CAAC;IAChC,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAc,AAAD;IACnB,MAAM,uHAAA,CAAA,UAAI,CAAC,iBAAiB,CAAC;IAC7B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QAAE,SAAS;IAAe,GAAG;QAAE,QAAQ;IAAI;AACtE", "debugId": null}}]}