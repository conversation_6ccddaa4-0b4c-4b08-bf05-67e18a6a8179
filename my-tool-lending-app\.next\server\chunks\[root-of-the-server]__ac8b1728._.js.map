{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Community%20Tool%20Lending%20Platform/my-tool-lending-app/src/lib/mongodb.ts"], "sourcesContent": ["import mongoose from 'mongoose';\r\n\r\n// Connection state tracking\r\nlet isConnected = false;\r\n\r\nconst connectMongoDB = async () => {\r\n  // If already connected, return early\r\n  if (isConnected && mongoose.connection.readyState === 1) {\r\n    return;\r\n  }\r\n\r\n  try {\r\n    const mongoUri = process.env.MONGODB_URI;\r\n\r\n    if (!mongoUri) {\r\n      throw new Error(\"MONGODB_URI environment variable is not defined\");\r\n    }\r\n\r\n    // Disconnect if there's an existing connection in a bad state\r\n    if (mongoose.connection.readyState !== 0) {\r\n      await mongoose.disconnect();\r\n    }\r\n\r\n    console.log(\"Connecting to MongoDB...\");\r\n\r\n    // Connect with optimized settings\r\n    await mongoose.connect(mongoUri, {\r\n      serverSelectionTimeoutMS: 10000, // 10 seconds\r\n      socketTimeoutMS: 45000, // 45 seconds\r\n      family: 4, // Use IPv4, skip trying IPv6\r\n    });\r\n\r\n    isConnected = true;\r\n    console.log(\"✅ MongoDB connected successfully\");\r\n\r\n  } catch (error) {\r\n    isConnected = false;\r\n    console.error(\"❌ MongoDB connection error:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Function to check connection status\r\nexport const getConnectionStatus = () => {\r\n  return {\r\n    isConnected,\r\n    readyState: mongoose.connection.readyState,\r\n    host: mongoose.connection.host,\r\n    port: mongoose.connection.port,\r\n    databaseName: mongoose.connection.db?.databaseName,\r\n  };\r\n};\r\n\r\n// Function to disconnect\r\nexport const disconnectMongoDB = async () => {\r\n  try {\r\n    await mongoose.disconnect();\r\n    isConnected = false;\r\n    console.log(\"🔌 Disconnected from MongoDB.\");\r\n  } catch (error) {\r\n    console.error(\"❌ Error disconnecting from MongoDB:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport default connectMongoDB;"], "names": [], "mappings": ";;;;;AAAA;;AAEA,4BAA4B;AAC5B,IAAI,cAAc;AAElB,MAAM,iBAAiB;IACrB,qCAAqC;IACrC,IAAI,eAAe,yGAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,UAAU,KAAK,GAAG;QACvD;IACF;IAEA,IAAI;QACF,MAAM,WAAW,QAAQ,GAAG,CAAC,WAAW;QAExC,IAAI,CAAC,UAAU;YACb,MAAM,IAAI,MAAM;QAClB;QAEA,8DAA8D;QAC9D,IAAI,yGAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,UAAU,KAAK,GAAG;YACxC,MAAM,yGAAA,CAAA,UAAQ,CAAC,UAAU;QAC3B;QAEA,QAAQ,GAAG,CAAC;QAEZ,kCAAkC;QAClC,MAAM,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,UAAU;YAC/B,0BAA0B;YAC1B,iBAAiB;YACjB,QAAQ;QACV;QAEA,cAAc;QACd,QAAQ,GAAG,CAAC;IAEd,EAAE,OAAO,OAAO;QACd,cAAc;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,MAAM;IACR;AACF;AAGO,MAAM,sBAAsB;IACjC,OAAO;QACL;QACA,YAAY,yGAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,UAAU;QAC1C,MAAM,yGAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,IAAI;QAC9B,MAAM,yGAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,IAAI;QAC9B,cAAc,yGAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,EAAE,EAAE;IACxC;AACF;AAGO,MAAM,oBAAoB;IAC/B,IAAI;QACF,MAAM,yGAAA,CAAA,UAAQ,CAAC,UAAU;QACzB,cAAc;QACd,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,MAAM;IACR;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 124, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Community%20Tool%20Lending%20Platform/my-tool-lending-app/src/models/tool.ts"], "sourcesContent": ["import mongoose, { Schema } from 'mongoose';\r\n\r\nconst toolSchema = new Schema(\r\n  {\r\n    name: {\r\n      type: String,\r\n      required: true,\r\n      trim: true,\r\n    },\r\n    description: {\r\n      type: String,\r\n      required: true,\r\n      trim: true,\r\n    },\r\n    category: {\r\n      type: String,\r\n      required: true,\r\n      enum: [\r\n        'Power Tools',\r\n        'Hand Tools',\r\n        'Garden Tools',\r\n        'Automotive',\r\n        'Construction',\r\n        'Electrical',\r\n        'Plumbing',\r\n        'Cleaning',\r\n        'Kitchen Appliances',\r\n        'Sports & Recreation',\r\n        'Other'\r\n      ],\r\n      default: 'Other'\r\n    },\r\n    condition: {\r\n      type: String,\r\n      enum: ['Excellent', 'Good', 'Fair', 'Poor'],\r\n      default: 'Good'\r\n    },\r\n    location: {\r\n      city: {\r\n        type: String,\r\n        required: true,\r\n        trim: true,\r\n      },\r\n      area: {\r\n        type: String,\r\n        required: true,\r\n        trim: true,\r\n      },\r\n      postalCode: {\r\n        type: String,\r\n        trim: true,\r\n      }\r\n    },\r\n    owner: {\r\n      name: {\r\n        type: String,\r\n        required: true,\r\n        trim: true,\r\n      },\r\n      email: {\r\n        type: String,\r\n        required: true,\r\n        trim: true,\r\n        lowercase: true,\r\n      },\r\n      phone: {\r\n        type: String,\r\n        trim: true,\r\n      }\r\n    },\r\n    availability: {\r\n      type: String,\r\n      enum: ['available', 'borrowed', 'maintenance', 'unavailable'],\r\n      default: 'available'\r\n    },\r\n    borrowingTerms: {\r\n      maxDuration: {\r\n        type: Number, // in days\r\n        default: 7\r\n      },\r\n      deposit: {\r\n        type: Number,\r\n        default: 0\r\n      },\r\n      instructions: {\r\n        type: String,\r\n        trim: true,\r\n      }\r\n    },\r\n    images: [{\r\n      type: String, // URLs to images\r\n    }],\r\n    tags: [{\r\n      type: String,\r\n      trim: true,\r\n      lowercase: true,\r\n    }],\r\n    // Legacy status field for backward compatibility\r\n    status: {\r\n      type: String,\r\n      default: 'available',\r\n    }\r\n  },\r\n  {\r\n    timestamps: true,\r\n  }\r\n);\r\n\r\n// Create indexes for better search performance\r\ntoolSchema.index({ name: 'text', description: 'text', tags: 'text' });\r\ntoolSchema.index({ category: 1 });\r\ntoolSchema.index({ 'location.city': 1 });\r\ntoolSchema.index({ availability: 1 });\r\ntoolSchema.index({ 'owner.email': 1 });\r\n\r\nconst Tool = mongoose.models.Tool || mongoose.model(\"Tool\", toolSchema);\r\n\r\nexport default Tool;"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,aAAa,IAAI,yGAAA,CAAA,SAAM,CAC3B;IACE,MAAM;QACJ,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA,aAAa;QACX,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA,UAAU;QACR,MAAM;QACN,UAAU;QACV,MAAM;YACJ;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,SAAS;IACX;IACA,WAAW;QACT,MAAM;QACN,MAAM;YAAC;YAAa;YAAQ;YAAQ;SAAO;QAC3C,SAAS;IACX;IACA,UAAU;QACR,MAAM;YACJ,MAAM;YACN,UAAU;YACV,MAAM;QACR;QACA,MAAM;YACJ,MAAM;YACN,UAAU;YACV,MAAM;QACR;QACA,YAAY;YACV,MAAM;YACN,MAAM;QACR;IACF;IACA,OAAO;QACL,MAAM;YACJ,MAAM;YACN,UAAU;YACV,MAAM;QACR;QACA,OAAO;YACL,MAAM;YACN,UAAU;YACV,MAAM;YACN,WAAW;QACb;QACA,OAAO;YACL,MAAM;YACN,MAAM;QACR;IACF;IACA,cAAc;QACZ,MAAM;QACN,MAAM;YAAC;YAAa;YAAY;YAAe;SAAc;QAC7D,SAAS;IACX;IACA,gBAAgB;QACd,aAAa;YACX,MAAM;YACN,SAAS;QACX;QACA,SAAS;YACP,MAAM;YACN,SAAS;QACX;QACA,cAAc;YACZ,MAAM;YACN,MAAM;QACR;IACF;IACA,QAAQ;QAAC;YACP,MAAM;QACR;KAAE;IACF,MAAM;QAAC;YACL,MAAM;YACN,MAAM;YACN,WAAW;QACb;KAAE;IACF,iDAAiD;IACjD,QAAQ;QACN,MAAM;QACN,SAAS;IACX;AACF,GACA;IACE,YAAY;AACd;AAGF,+CAA+C;AAC/C,WAAW,KAAK,CAAC;IAAE,MAAM;IAAQ,aAAa;IAAQ,MAAM;AAAO;AACnE,WAAW,KAAK,CAAC;IAAE,UAAU;AAAE;AAC/B,WAAW,KAAK,CAAC;IAAE,iBAAiB;AAAE;AACtC,WAAW,KAAK,CAAC;IAAE,cAAc;AAAE;AACnC,WAAW,KAAK,CAAC;IAAE,eAAe;AAAE;AAEpC,MAAM,OAAO,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAC,QAAQ;uCAE7C", "debugId": null}}, {"offset": {"line": 279, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Community%20Tool%20Lending%20Platform/my-tool-lending-app/src/app/api/tools/route.ts"], "sourcesContent": ["import connectMongoDB from \"@/lib/mongodb\";\r\nimport Tool from \"@/models/tool\";\r\nimport { NextResponse } from \"next/server\";\r\n\r\n// Function to CREATE a new tool\r\nexport async function POST(request: Request) {\r\n  try {\r\n    console.log(\"📝 POST /api/tools - Creating new tool\");\r\n    const toolData = await request.json();\r\n    console.log(\"📋 Tool data:\", toolData);\r\n\r\n    // Validate required fields\r\n    const { name, description, location, owner } = toolData;\r\n    if (!name || !description || !location?.city || !location?.area || !owner?.name || !owner?.email) {\r\n      return NextResponse.json({ error: \"Missing required fields\" }, { status: 400 });\r\n    }\r\n\r\n    await connectMongoDB();\r\n    console.log(\"🔗 Database connected for POST\");\r\n\r\n    // Set availability based on legacy status if provided\r\n    if (toolData.status && !toolData.availability) {\r\n      toolData.availability = toolData.status;\r\n    }\r\n\r\n    const newTool = await Tool.create(toolData);\r\n    console.log(\"✅ Tool created successfully:\", newTool._id);\r\n\r\n    return NextResponse.json({ message: \"Tool Created\", tool: newTool }, { status: 201 });\r\n  } catch (error) {\r\n    console.error(\"❌ Error creating tool:\", error);\r\n    return NextResponse.json({\r\n      error: \"Failed to create tool\",\r\n      details: error instanceof Error ? error.message : String(error)\r\n    }, { status: 500 });\r\n  }\r\n}\r\n\r\n// Function to GET all tools\r\nexport async function GET() {\r\n  try {\r\n    console.log(\"📖 GET /api/tools - Fetching all tools\");\r\n    await connectMongoDB();\r\n    console.log(\"🔗 Database connected for GET\");\r\n\r\n    const tools = await Tool.find().sort({ createdAt: -1 });\r\n    console.log(`📊 Found ${tools.length} tools`);\r\n\r\n    return NextResponse.json({ tools });\r\n  } catch (error) {\r\n    console.error(\"❌ Error fetching tools:\", error);\r\n    return NextResponse.json({ error: \"Failed to fetch tools\", tools: [] }, { status: 500 });\r\n  }\r\n}\r\n\r\n// Function to DELETE a tool\r\nexport async function DELETE(request: Request) {\r\n  const url = new URL(request.url);\r\n  const id = url.searchParams.get('id');\r\n  await connectMongoDB();\r\n  await Tool.findByIdAndDelete(id);\r\n  return NextResponse.json({ message: \"Tool deleted\" }, { status: 200 });\r\n}"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;AAGO,eAAe,KAAK,OAAgB;IACzC,IAAI;QACF,QAAQ,GAAG,CAAC;QACZ,MAAM,WAAW,MAAM,QAAQ,IAAI;QACnC,QAAQ,GAAG,CAAC,iBAAiB;QAE7B,2BAA2B;QAC3B,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG;QAC/C,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,UAAU,QAAQ,CAAC,UAAU,QAAQ,CAAC,OAAO,QAAQ,CAAC,OAAO,OAAO;YAChG,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAA0B,GAAG;gBAAE,QAAQ;YAAI;QAC/E;QAEA,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAc,AAAD;QACnB,QAAQ,GAAG,CAAC;QAEZ,sDAAsD;QACtD,IAAI,SAAS,MAAM,IAAI,CAAC,SAAS,YAAY,EAAE;YAC7C,SAAS,YAAY,GAAG,SAAS,MAAM;QACzC;QAEA,MAAM,UAAU,MAAM,uHAAA,CAAA,UAAI,CAAC,MAAM,CAAC;QAClC,QAAQ,GAAG,CAAC,gCAAgC,QAAQ,GAAG;QAEvD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,SAAS;YAAgB,MAAM;QAAQ,GAAG;YAAE,QAAQ;QAAI;IACrF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,OAAO;YACP,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO;QAC3D,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF;AAGO,eAAe;IACpB,IAAI;QACF,QAAQ,GAAG,CAAC;QACZ,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAc,AAAD;QACnB,QAAQ,GAAG,CAAC;QAEZ,MAAM,QAAQ,MAAM,uHAAA,CAAA,UAAI,CAAC,IAAI,GAAG,IAAI,CAAC;YAAE,WAAW,CAAC;QAAE;QACrD,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,MAAM,MAAM,CAAC,MAAM,CAAC;QAE5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE;QAAM;IACnC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;YAAyB,OAAO,EAAE;QAAC,GAAG;YAAE,QAAQ;QAAI;IACxF;AACF;AAGO,eAAe,OAAO,OAAgB;IAC3C,MAAM,MAAM,IAAI,IAAI,QAAQ,GAAG;IAC/B,MAAM,KAAK,IAAI,YAAY,CAAC,GAAG,CAAC;IAChC,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAc,AAAD;IACnB,MAAM,uHAAA,CAAA,UAAI,CAAC,iBAAiB,CAAC;IAC7B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QAAE,SAAS;IAAe,GAAG;QAAE,QAAQ;IAAI;AACtE", "debugId": null}}]}