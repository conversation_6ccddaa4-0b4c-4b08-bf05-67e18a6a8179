{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Community%20Tool%20Lending%20Platform/my-tool-lending-app/src/lib/mongodb.ts"], "sourcesContent": ["import mongoose from 'mongoose';\r\n\r\n// Connection state tracking\r\nlet isConnected = false;\r\n\r\nconst connectMongoDB = async () => {\r\n  // If already connected, return early\r\n  if (isConnected && mongoose.connection.readyState === 1) {\r\n    return;\r\n  }\r\n\r\n  try {\r\n    const mongoUri = process.env.MONGODB_URI;\r\n\r\n    if (!mongoUri) {\r\n      throw new Error(\"MONGODB_URI environment variable is not defined\");\r\n    }\r\n\r\n    // Disconnect if there's an existing connection in a bad state\r\n    if (mongoose.connection.readyState !== 0) {\r\n      await mongoose.disconnect();\r\n    }\r\n\r\n    console.log(\"Connecting to MongoDB...\");\r\n\r\n    // Connect with optimized settings\r\n    await mongoose.connect(mongoUri, {\r\n      serverSelectionTimeoutMS: 10000, // 10 seconds\r\n      socketTimeoutMS: 45000, // 45 seconds\r\n      family: 4, // Use IPv4, skip trying IPv6\r\n    });\r\n\r\n    isConnected = true;\r\n    console.log(\"✅ MongoDB connected successfully\");\r\n\r\n  } catch (error) {\r\n    isConnected = false;\r\n    console.error(\"❌ MongoDB connection error:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Function to check connection status\r\nexport const getConnectionStatus = () => {\r\n  return {\r\n    isConnected,\r\n    readyState: mongoose.connection.readyState,\r\n    host: mongoose.connection.host,\r\n    port: mongoose.connection.port,\r\n    databaseName: mongoose.connection.db?.databaseName,\r\n  };\r\n};\r\n\r\n// Function to disconnect\r\nexport const disconnectMongoDB = async () => {\r\n  try {\r\n    await mongoose.disconnect();\r\n    isConnected = false;\r\n    console.log(\"🔌 Disconnected from MongoDB.\");\r\n  } catch (error) {\r\n    console.error(\"❌ Error disconnecting from MongoDB:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport default connectMongoDB;"], "names": [], "mappings": ";;;;;AAAA;;AAEA,4BAA4B;AAC5B,IAAI,cAAc;AAElB,MAAM,iBAAiB;IACrB,qCAAqC;IACrC,IAAI,eAAe,yGAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,UAAU,KAAK,GAAG;QACvD;IACF;IAEA,IAAI;QACF,MAAM,WAAW,QAAQ,GAAG,CAAC,WAAW;QAExC,IAAI,CAAC,UAAU;YACb,MAAM,IAAI,MAAM;QAClB;QAEA,8DAA8D;QAC9D,IAAI,yGAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,UAAU,KAAK,GAAG;YACxC,MAAM,yGAAA,CAAA,UAAQ,CAAC,UAAU;QAC3B;QAEA,QAAQ,GAAG,CAAC;QAEZ,kCAAkC;QAClC,MAAM,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,UAAU;YAC/B,0BAA0B;YAC1B,iBAAiB;YACjB,QAAQ;QACV;QAEA,cAAc;QACd,QAAQ,GAAG,CAAC;IAEd,EAAE,OAAO,OAAO;QACd,cAAc;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,MAAM;IACR;AACF;AAGO,MAAM,sBAAsB;IACjC,OAAO;QACL;QACA,YAAY,yGAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,UAAU;QAC1C,MAAM,yGAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,IAAI;QAC9B,MAAM,yGAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,IAAI;QAC9B,cAAc,yGAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,EAAE,EAAE;IACxC;AACF;AAGO,MAAM,oBAAoB;IAC/B,IAAI;QACF,MAAM,yGAAA,CAAA,UAAQ,CAAC,UAAU;QACzB,cAAc;QACd,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,MAAM;IACR;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 124, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Community%20Tool%20Lending%20Platform/my-tool-lending-app/src/models/tool.ts"], "sourcesContent": ["import mongoose, { Schema } from 'mongoose';\r\n\r\nconst toolSchema = new Schema(\r\n  {\r\n    name: {\r\n      type: String,\r\n      required: true,\r\n      trim: true,\r\n    },\r\n    description: {\r\n      type: String,\r\n      required: true,\r\n      trim: true,\r\n    },\r\n    category: {\r\n      type: String,\r\n      required: true,\r\n      enum: [\r\n        'Power Tools',\r\n        'Hand Tools',\r\n        'Garden Tools',\r\n        'Automotive',\r\n        'Construction',\r\n        'Electrical',\r\n        'Plumbing',\r\n        'Cleaning',\r\n        'Kitchen Appliances',\r\n        'Sports & Recreation',\r\n        'Other'\r\n      ],\r\n      default: 'Other'\r\n    },\r\n    condition: {\r\n      type: String,\r\n      enum: ['Excellent', 'Good', 'Fair', 'Poor'],\r\n      default: 'Good'\r\n    },\r\n    location: {\r\n      city: {\r\n        type: String,\r\n        required: true,\r\n        trim: true,\r\n      },\r\n      area: {\r\n        type: String,\r\n        required: true,\r\n        trim: true,\r\n      },\r\n      postalCode: {\r\n        type: String,\r\n        trim: true,\r\n      }\r\n    },\r\n    owner: {\r\n      name: {\r\n        type: String,\r\n        required: true,\r\n        trim: true,\r\n      },\r\n      email: {\r\n        type: String,\r\n        required: true,\r\n        trim: true,\r\n        lowercase: true,\r\n      },\r\n      phone: {\r\n        type: String,\r\n        trim: true,\r\n      }\r\n    },\r\n    availability: {\r\n      type: String,\r\n      enum: ['available', 'borrowed', 'maintenance', 'unavailable'],\r\n      default: 'available'\r\n    },\r\n    borrowingTerms: {\r\n      maxDuration: {\r\n        type: Number, // in days\r\n        default: 7\r\n      },\r\n      deposit: {\r\n        type: Number,\r\n        default: 0\r\n      },\r\n      instructions: {\r\n        type: String,\r\n        trim: true,\r\n      }\r\n    },\r\n    images: [{\r\n      type: String, // URLs to images\r\n    }],\r\n    tags: [{\r\n      type: String,\r\n      trim: true,\r\n      lowercase: true,\r\n    }],\r\n    // Legacy status field for backward compatibility\r\n    status: {\r\n      type: String,\r\n      default: 'available',\r\n    }\r\n  },\r\n  {\r\n    timestamps: true,\r\n  }\r\n);\r\n\r\n// Create indexes for better search performance\r\ntoolSchema.index({ name: 'text', description: 'text', tags: 'text' });\r\ntoolSchema.index({ category: 1 });\r\ntoolSchema.index({ 'location.city': 1 });\r\ntoolSchema.index({ availability: 1 });\r\ntoolSchema.index({ 'owner.email': 1 });\r\n\r\nconst Tool = mongoose.models.Tool || mongoose.model(\"Tool\", toolSchema);\r\n\r\nexport default Tool;"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,aAAa,IAAI,yGAAA,CAAA,SAAM,CAC3B;IACE,MAAM;QACJ,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA,aAAa;QACX,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA,UAAU;QACR,MAAM;QACN,UAAU;QACV,MAAM;YACJ;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,SAAS;IACX;IACA,WAAW;QACT,MAAM;QACN,MAAM;YAAC;YAAa;YAAQ;YAAQ;SAAO;QAC3C,SAAS;IACX;IACA,UAAU;QACR,MAAM;YACJ,MAAM;YACN,UAAU;YACV,MAAM;QACR;QACA,MAAM;YACJ,MAAM;YACN,UAAU;YACV,MAAM;QACR;QACA,YAAY;YACV,MAAM;YACN,MAAM;QACR;IACF;IACA,OAAO;QACL,MAAM;YACJ,MAAM;YACN,UAAU;YACV,MAAM;QACR;QACA,OAAO;YACL,MAAM;YACN,UAAU;YACV,MAAM;YACN,WAAW;QACb;QACA,OAAO;YACL,MAAM;YACN,MAAM;QACR;IACF;IACA,cAAc;QACZ,MAAM;QACN,MAAM;YAAC;YAAa;YAAY;YAAe;SAAc;QAC7D,SAAS;IACX;IACA,gBAAgB;QACd,aAAa;YACX,MAAM;YACN,SAAS;QACX;QACA,SAAS;YACP,MAAM;YACN,SAAS;QACX;QACA,cAAc;YACZ,MAAM;YACN,MAAM;QACR;IACF;IACA,QAAQ;QAAC;YACP,MAAM;QACR;KAAE;IACF,MAAM;QAAC;YACL,MAAM;YACN,MAAM;YACN,WAAW;QACb;KAAE;IACF,iDAAiD;IACjD,QAAQ;QACN,MAAM;QACN,SAAS;IACX;AACF,GACA;IACE,YAAY;AACd;AAGF,+CAA+C;AAC/C,WAAW,KAAK,CAAC;IAAE,MAAM;IAAQ,aAAa;IAAQ,MAAM;AAAO;AACnE,WAAW,KAAK,CAAC;IAAE,UAAU;AAAE;AAC/B,WAAW,KAAK,CAAC;IAAE,iBAAiB;AAAE;AACtC,WAAW,KAAK,CAAC;IAAE,cAAc;AAAE;AACnC,WAAW,KAAK,CAAC;IAAE,eAAe;AAAE;AAEpC,MAAM,OAAO,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAC,QAAQ;uCAE7C", "debugId": null}}, {"offset": {"line": 279, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Community%20Tool%20Lending%20Platform/my-tool-lending-app/src/app/api/tools/search/route.ts"], "sourcesContent": ["import connectMongoDB from \"@/lib/mongodb\";\nimport Tool from \"@/models/tool\";\nimport { NextResponse } from \"next/server\";\n\nexport async function GET(request: Request) {\n  try {\n    console.log(\"🔍 GET /api/tools/search - Searching tools\");\n    \n    const { searchParams } = new URL(request.url);\n    const query = searchParams.get('q') || '';\n    const category = searchParams.get('category') || '';\n    const city = searchParams.get('city') || '';\n    const availability = searchParams.get('availability') || '';\n    const condition = searchParams.get('condition') || '';\n    const sortBy = searchParams.get('sortBy') || 'createdAt';\n    const sortOrder = searchParams.get('sortOrder') || 'desc';\n    \n    console.log(\"🔍 Search parameters:\", { \n      query, category, city, availability, condition, sortBy, sortOrder \n    });\n    \n    await connectMongoDB();\n    console.log(\"🔗 Database connected for search\");\n    \n    // Build search filter\n    const filter: any = {};\n    \n    // Text search across name, description, and tags\n    if (query) {\n      filter.$or = [\n        { name: { $regex: query, $options: 'i' } },\n        { description: { $regex: query, $options: 'i' } },\n        { tags: { $in: [new RegExp(query, 'i')] } }\n      ];\n    }\n    \n    // Category filter\n    if (category && category !== 'all') {\n      filter.category = category;\n    }\n    \n    // Location filter\n    if (city) {\n      filter['location.city'] = { $regex: city, $options: 'i' };\n    }\n    \n    // Availability filter\n    if (availability && availability !== 'all') {\n      filter.availability = availability;\n    }\n    \n    // Condition filter\n    if (condition && condition !== 'all') {\n      filter.condition = condition;\n    }\n    \n    // Build sort object\n    const sort: any = {};\n    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;\n    \n    console.log(\"🔍 Final filter:\", filter);\n    console.log(\"📊 Sort:\", sort);\n    \n    const tools = await Tool.find(filter).sort(sort);\n    console.log(`📊 Found ${tools.length} tools matching search criteria`);\n    \n    // Get aggregated data for filters\n    const aggregationData = await Tool.aggregate([\n      {\n        $group: {\n          _id: null,\n          categories: { $addToSet: \"$category\" },\n          cities: { $addToSet: \"$location.city\" },\n          conditions: { $addToSet: \"$condition\" },\n          availabilities: { $addToSet: \"$availability\" },\n          totalTools: { $sum: 1 }\n        }\n      }\n    ]);\n    \n    const filterOptions = aggregationData[0] || {\n      categories: [],\n      cities: [],\n      conditions: [],\n      availabilities: [],\n      totalTools: 0\n    };\n    \n    return NextResponse.json({ \n      tools,\n      filterOptions,\n      searchParams: { query, category, city, availability, condition, sortBy, sortOrder },\n      totalResults: tools.length\n    });\n    \n  } catch (error) {\n    console.error(\"❌ Error searching tools:\", error);\n    return NextResponse.json({ \n      error: \"Failed to search tools\", \n      tools: [],\n      filterOptions: {},\n      totalResults: 0\n    }, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEO,eAAe,IAAI,OAAgB;IACxC,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,QAAQ,aAAa,GAAG,CAAC,QAAQ;QACvC,MAAM,WAAW,aAAa,GAAG,CAAC,eAAe;QACjD,MAAM,OAAO,aAAa,GAAG,CAAC,WAAW;QACzC,MAAM,eAAe,aAAa,GAAG,CAAC,mBAAmB;QACzD,MAAM,YAAY,aAAa,GAAG,CAAC,gBAAgB;QACnD,MAAM,SAAS,aAAa,GAAG,CAAC,aAAa;QAC7C,MAAM,YAAY,aAAa,GAAG,CAAC,gBAAgB;QAEnD,QAAQ,GAAG,CAAC,yBAAyB;YACnC;YAAO;YAAU;YAAM;YAAc;YAAW;YAAQ;QAC1D;QAEA,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAc,AAAD;QACnB,QAAQ,GAAG,CAAC;QAEZ,sBAAsB;QACtB,MAAM,SAAc,CAAC;QAErB,iDAAiD;QACjD,IAAI,OAAO;YACT,OAAO,GAAG,GAAG;gBACX;oBAAE,MAAM;wBAAE,QAAQ;wBAAO,UAAU;oBAAI;gBAAE;gBACzC;oBAAE,aAAa;wBAAE,QAAQ;wBAAO,UAAU;oBAAI;gBAAE;gBAChD;oBAAE,MAAM;wBAAE,KAAK;4BAAC,IAAI,OAAO,OAAO;yBAAK;oBAAC;gBAAE;aAC3C;QACH;QAEA,kBAAkB;QAClB,IAAI,YAAY,aAAa,OAAO;YAClC,OAAO,QAAQ,GAAG;QACpB;QAEA,kBAAkB;QAClB,IAAI,MAAM;YACR,MAAM,CAAC,gBAAgB,GAAG;gBAAE,QAAQ;gBAAM,UAAU;YAAI;QAC1D;QAEA,sBAAsB;QACtB,IAAI,gBAAgB,iBAAiB,OAAO;YAC1C,OAAO,YAAY,GAAG;QACxB;QAEA,mBAAmB;QACnB,IAAI,aAAa,cAAc,OAAO;YACpC,OAAO,SAAS,GAAG;QACrB;QAEA,oBAAoB;QACpB,MAAM,OAAY,CAAC;QACnB,IAAI,CAAC,OAAO,GAAG,cAAc,QAAQ,IAAI,CAAC;QAE1C,QAAQ,GAAG,CAAC,oBAAoB;QAChC,QAAQ,GAAG,CAAC,YAAY;QAExB,MAAM,QAAQ,MAAM,uHAAA,CAAA,UAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC;QAC3C,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,MAAM,MAAM,CAAC,+BAA+B,CAAC;QAErE,kCAAkC;QAClC,MAAM,kBAAkB,MAAM,uHAAA,CAAA,UAAI,CAAC,SAAS,CAAC;YAC3C;gBACE,QAAQ;oBACN,KAAK;oBACL,YAAY;wBAAE,WAAW;oBAAY;oBACrC,QAAQ;wBAAE,WAAW;oBAAiB;oBACtC,YAAY;wBAAE,WAAW;oBAAa;oBACtC,gBAAgB;wBAAE,WAAW;oBAAgB;oBAC7C,YAAY;wBAAE,MAAM;oBAAE;gBACxB;YACF;SACD;QAED,MAAM,gBAAgB,eAAe,CAAC,EAAE,IAAI;YAC1C,YAAY,EAAE;YACd,QAAQ,EAAE;YACV,YAAY,EAAE;YACd,gBAAgB,EAAE;YAClB,YAAY;QACd;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB;YACA;YACA,cAAc;gBAAE;gBAAO;gBAAU;gBAAM;gBAAc;gBAAW;gBAAQ;YAAU;YAClF,cAAc,MAAM,MAAM;QAC5B;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,OAAO;YACP,OAAO,EAAE;YACT,eAAe,CAAC;YAChB,cAAc;QAChB,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}