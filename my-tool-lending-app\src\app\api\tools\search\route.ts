import connectMongoDB from "@/lib/mongodb";
import Tool from "@/models/tool";
import { NextResponse } from "next/server";

export async function GET(request: Request) {
  try {
    console.log("🔍 GET /api/tools/search - Searching tools");
    
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('q') || '';
    const category = searchParams.get('category') || '';
    const city = searchParams.get('city') || '';
    const availability = searchParams.get('availability') || '';
    const condition = searchParams.get('condition') || '';
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';
    
    console.log("🔍 Search parameters:", { 
      query, category, city, availability, condition, sortBy, sortOrder 
    });
    
    await connectMongoDB();
    console.log("🔗 Database connected for search");
    
    // Build search filter
    const filter: any = {};
    
    // Text search across name, description, and tags
    if (query) {
      filter.$or = [
        { name: { $regex: query, $options: 'i' } },
        { description: { $regex: query, $options: 'i' } },
        { tags: { $in: [new RegExp(query, 'i')] } }
      ];
    }
    
    // Category filter
    if (category && category !== 'all') {
      filter.category = category;
    }
    
    // Location filter
    if (city) {
      filter['location.city'] = { $regex: city, $options: 'i' };
    }
    
    // Availability filter
    if (availability && availability !== 'all') {
      filter.availability = availability;
    }
    
    // Condition filter
    if (condition && condition !== 'all') {
      filter.condition = condition;
    }
    
    // Build sort object
    const sort: any = {};
    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;
    
    console.log("🔍 Final filter:", filter);
    console.log("📊 Sort:", sort);
    
    const tools = await Tool.find(filter).sort(sort);
    console.log(`📊 Found ${tools.length} tools matching search criteria`);
    
    // Get aggregated data for filters
    const aggregationData = await Tool.aggregate([
      {
        $group: {
          _id: null,
          categories: { $addToSet: "$category" },
          cities: { $addToSet: "$location.city" },
          conditions: { $addToSet: "$condition" },
          availabilities: { $addToSet: "$availability" },
          totalTools: { $sum: 1 }
        }
      }
    ]);
    
    const filterOptions = aggregationData[0] || {
      categories: [],
      cities: [],
      conditions: [],
      availabilities: [],
      totalTools: 0
    };
    
    return NextResponse.json({ 
      tools,
      filterOptions,
      searchParams: { query, category, city, availability, condition, sortBy, sortOrder },
      totalResults: tools.length
    });
    
  } catch (error) {
    console.error("❌ Error searching tools:", error);
    return NextResponse.json({ 
      error: "Failed to search tools", 
      tools: [],
      filterOptions: {},
      totalResults: 0
    }, { status: 500 });
  }
}
