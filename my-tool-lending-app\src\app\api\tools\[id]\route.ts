import connectMongoDB from "@/lib/mongodb";
import Tool from "@/models/tool";
import { NextResponse } from "next/server";

// GET a single tool by ID
export async function GET(request: Request, { params }: { params: Promise<{ id: string }> }) {
  const { id } = await params;
  await connectMongoDB();
  const tool = await Tool.findOne({ _id: id });
  return NextResponse.json({ tool }, { status: 200 });
}

// UPDATE a tool by ID
export async function PUT(request: Request, { params }: { params: Promise<{ id: string }> }) {
  const { id } = await params;
  const { newName: name, newDescription: description } = await request.json();
  await connectMongoDB();
  await Tool.findByIdAndUpdate(id, { name, description });
  return NextResponse.json({ message: "Tool updated" }, { status: 200 });
}
