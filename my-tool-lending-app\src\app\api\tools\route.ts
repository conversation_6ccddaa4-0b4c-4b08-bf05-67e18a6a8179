import connectMongoDB from "@/lib/mongodb";
import Tool from "@/models/tool";
import { NextResponse } from "next/server";

// Function to CREATE a new tool
export async function POST(request: Request) {
  try {
    console.log("📝 POST /api/tools - Creating new tool");
    const { name, description } = await request.json();
    console.log("📋 Tool data:", { name, description });

    await connectMongoDB();
    console.log("🔗 Database connected for POST");

    const newTool = await Tool.create({ name, description });
    console.log("✅ Tool created successfully:", newTool._id);

    return NextResponse.json({ message: "Tool Created", tool: newTool }, { status: 201 });
  } catch (error) {
    console.error("❌ Error creating tool:", error);
    return NextResponse.json({ error: "Failed to create tool" }, { status: 500 });
  }
}

// Function to GET all tools
export async function GET() {
  try {
    console.log("📖 GET /api/tools - Fetching all tools");
    await connectMongoDB();
    console.log("🔗 Database connected for GET");

    const tools = await Tool.find().sort({ createdAt: -1 });
    console.log(`📊 Found ${tools.length} tools`);

    return NextResponse.json({ tools });
  } catch (error) {
    console.error("❌ Error fetching tools:", error);
    return NextResponse.json({ error: "Failed to fetch tools", tools: [] }, { status: 500 });
  }
}

// Function to DELETE a tool
export async function DELETE(request: Request) {
  const url = new URL(request.url);
  const id = url.searchParams.get('id');
  await connectMongoDB();
  await Tool.findByIdAndDelete(id);
  return NextResponse.json({ message: "Tool deleted" }, { status: 200 });
}