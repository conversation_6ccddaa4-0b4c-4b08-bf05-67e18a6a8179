import connectMongoDB from "@/lib/mongodb";
import Tool from "@/models/tool";
import { NextResponse } from "next/server";

// Function to CREATE a new tool
export async function POST(request: Request) {
  const { name, description } = await request.json();
  await connectMongoDB();
  await Tool.create({ name, description });
  return NextResponse.json({ message: "Tool Created" }, { status: 201 });
}

// Function to GET all tools
export async function GET() {
  await connectMongoDB();
  const tools = await Tool.find();
  return NextResponse.json({ tools });
}