{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Community%20Tool%20Lending%20Platform/my-tool-lending-app/src/components/RemoveBtn.tsx"], "sourcesContent": ["'use client';\n\nimport { HiOutlineTrash } from 'react-icons/hi';\nimport { useRouter } from 'next/navigation';\n\nexport default function RemoveBtn({ id }: { id: string }) {\n    const router = useRouter();\n    const removeTool = async () => {\n        const confirmed = confirm('Are you sure you want to delete this tool?');\n\n        if (confirmed) {\n            const res = await fetch(`/api/tools?id=${id}`, {\n                method: 'DELETE',\n            });\n            \n            if (res.ok) {\n                router.refresh();\n            }\n        }\n    };\n\n    return (\n        <button onClick={removeTool} className=\"text-red-400 hover:text-red-600\">\n            <HiOutlineTrash size={24} />\n        </button>\n    );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS,UAAU,EAAE,EAAE,EAAkB;IACpD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,aAAa;QACf,MAAM,YAAY,QAAQ;QAE1B,IAAI,WAAW;YACX,MAAM,MAAM,MAAM,MAAM,CAAC,cAAc,EAAE,IAAI,EAAE;gBAC3C,QAAQ;YACZ;YAEA,IAAI,IAAI,EAAE,EAAE;gBACR,OAAO,OAAO;YAClB;QACJ;IACJ;IAEA,qBACI,8OAAC;QAAO,SAAS;QAAY,WAAU;kBACnC,cAAA,8OAAC,8IAAA,CAAA,iBAAc;YAAC,MAAM;;;;;;;;;;;AAGlC", "debugId": null}}]}