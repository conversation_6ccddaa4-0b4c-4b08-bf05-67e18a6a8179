{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Community%20Tool%20Lending%20Platform/my-tool-lending-app/src/components/RemoveBtn.tsx"], "sourcesContent": ["'use client';\n\nimport { HiOutlineTrash } from 'react-icons/hi';\nimport { useRouter } from 'next/navigation';\n\nexport default function RemoveBtn({ id }: { id: string }) {\n    const router = useRouter();\n    const removeTool = async () => {\n        const confirmed = confirm('Are you sure you want to delete this tool?');\n\n        if (confirmed) {\n            const res = await fetch(`/api/tools?id=${id}`, {\n                method: 'DELETE',\n            });\n            \n            if (res.ok) {\n                router.refresh();\n            }\n        }\n    };\n\n    return (\n        <button\n            onClick={removeTool}\n            className=\"text-red-600 hover:text-red-700 transition-colors duration-200\"\n            title=\"Delete Tool\"\n        >\n            <HiOutlineTrash size={18} />\n        </button>\n    );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS,UAAU,EAAE,EAAE,EAAkB;;IACpD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,aAAa;QACf,MAAM,YAAY,QAAQ;QAE1B,IAAI,WAAW;YACX,MAAM,MAAM,MAAM,MAAM,CAAC,cAAc,EAAE,IAAI,EAAE;gBAC3C,QAAQ;YACZ;YAEA,IAAI,IAAI,EAAE,EAAE;gBACR,OAAO,OAAO;YAClB;QACJ;IACJ;IAEA,qBACI,6LAAC;QACG,SAAS;QACT,WAAU;QACV,OAAM;kBAEN,cAAA,6LAAC,iJAAA,CAAA,iBAAc;YAAC,MAAM;;;;;;;;;;;AAGlC;GAzBwB;;QACL,qIAAA,CAAA,YAAS;;;KADJ", "debugId": null}}]}