{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Community%20Tool%20Lending%20Platform/my-tool-lending-app/src/components/RemoveBtn.tsx"], "sourcesContent": ["'use client';\n\nimport { HiOutlineTrash } from 'react-icons/hi';\nimport { useRouter } from 'next/navigation';\n\nexport default function RemoveBtn({ id }: { id: string }) {\n    const router = useRouter();\n    const removeTool = async () => {\n        const confirmed = confirm('Are you sure you want to delete this tool?');\n\n        if (confirmed) {\n            const res = await fetch(`/api/tools?id=${id}`, {\n                method: 'DELETE',\n            });\n            \n            if (res.ok) {\n                router.refresh();\n            }\n        }\n    };\n\n    return (\n        <button\n            onClick={removeTool}\n            className=\"text-red-600 hover:text-red-700 transition-colors duration-200\"\n            title=\"Delete Tool\"\n        >\n            <HiOutlineTrash size={18} />\n        </button>\n    );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS,UAAU,EAAE,EAAE,EAAkB;;IACpD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,aAAa;QACf,MAAM,YAAY,QAAQ;QAE1B,IAAI,WAAW;YACX,MAAM,MAAM,MAAM,MAAM,CAAC,cAAc,EAAE,IAAI,EAAE;gBAC3C,QAAQ;YACZ;YAEA,IAAI,IAAI,EAAE,EAAE;gBACR,OAAO,OAAO;YAClB;QACJ;IACJ;IAEA,qBACI,6LAAC;QACG,SAAS;QACT,WAAU;QACV,OAAM;kBAEN,cAAA,6LAAC,iJAAA,CAAA,iBAAc;YAAC,MAAM;;;;;;;;;;;AAGlC;GAzBwB;;QACL,qIAAA,CAAA,YAAS;;;KADJ", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Community%20Tool%20Lending%20Platform/my-tool-lending-app/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useCallback } from 'react';\nimport Link from 'next/link';\nimport RemoveBtn from '@/components/RemoveBtn';\nimport SearchAndFilter from '@/components/SearchAndFilter';\nimport { <PERSON><PERSON><PERSON>cilAlt, HiLocationMarker, <PERSON><PERSON>ser, <PERSON><PERSON>lock, HiCurrencyDollar } from 'react-icons/hi';\n\ninterface Tool {\n  _id: string;\n  name: string;\n  description: string;\n  category: string;\n  condition: string;\n  location: {\n    city: string;\n    area: string;\n    postalCode?: string;\n  };\n  owner: {\n    name: string;\n    email: string;\n    phone?: string;\n  };\n  availability: string;\n  borrowingTerms: {\n    maxDuration: number;\n    deposit: number;\n    instructions?: string;\n  };\n  tags: string[];\n  createdAt: string;\n  updatedAt: string;\n}\n\ninterface SearchParams {\n  query: string;\n  category: string;\n  city: string;\n  availability: string;\n  condition: string;\n  sortBy: string;\n  sortOrder: string;\n}\n\nexport default function Home() {\n  const [tools, setTools] = useState<Tool[]>([]);\n  const [filterOptions, setFilterOptions] = useState<any>({});\n  const [isLoading, setIsLoading] = useState(false);\n  const [totalResults, setTotalResults] = useState(0);\n\n  // Search function\n  const handleSearch = useCallback(async (searchParams: SearchParams) => {\n    setIsLoading(true);\n    try {\n      const queryParams = new URLSearchParams();\n      Object.entries(searchParams).forEach(([key, value]) => {\n        if (value && value !== 'all') {\n          queryParams.append(key, value);\n        }\n      });\n\n      const response = await fetch(`/api/tools/search?${queryParams.toString()}`);\n      const data = await response.json();\n\n      if (response.ok) {\n        setTools(data.tools || []);\n        setFilterOptions(data.filterOptions || {});\n        setTotalResults(data.totalResults || 0);\n      } else {\n        console.error('Search failed:', data.error);\n        setTools([]);\n        setTotalResults(0);\n      }\n    } catch (error) {\n      console.error('Search error:', error);\n      setTools([]);\n      setTotalResults(0);\n    } finally {\n      setIsLoading(false);\n    }\n  }, []);\n\n  // Load initial data\n  useEffect(() => {\n    handleSearch({\n      query: '',\n      category: 'all',\n      city: '',\n      availability: 'all',\n      condition: 'all',\n      sortBy: 'createdAt',\n      sortOrder: 'desc'\n    });\n  }, [handleSearch]);\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\n      <div className=\"container mx-auto px-4 py-8\">\n        {/* Hero Section */}\n        <div className=\"text-center mb-12\">\n          <h1 className=\"text-5xl font-bold text-gray-800 mb-4\">\n            Community Tools\n          </h1>\n          <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n            Share tools, build community. Discover what your neighbors have to offer and contribute your own tools to help others.\n          </p>\n        </div>\n\n        {tools && tools.length > 0 ? (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n            {tools.map((tool: any) => (\n              <div key={tool._id} className=\"bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 overflow-hidden\">\n                {/* Tool Header */}\n                <div className=\"bg-gradient-to-r from-blue-500 to-purple-600 p-4\">\n                  <h2 className=\"text-xl font-bold text-white truncate\">{tool.name}</h2>\n                  <div className=\"flex items-center mt-2\">\n                    <span className={`px-3 py-1 rounded-full text-xs font-medium ${\n                      tool.status === 'available'\n                        ? 'bg-green-100 text-green-800'\n                        : tool.status === 'borrowed'\n                        ? 'bg-yellow-100 text-yellow-800'\n                        : 'bg-red-100 text-red-800'\n                    }`}>\n                      {tool.status?.charAt(0).toUpperCase() + tool.status?.slice(1) || 'Available'}\n                    </span>\n                  </div>\n                </div>\n\n                {/* Tool Content */}\n                <div className=\"p-6\">\n                  <p className=\"text-gray-600 text-sm leading-relaxed mb-4 line-clamp-3\">\n                    {tool.description}\n                  </p>\n\n                  {/* Action Buttons */}\n                  <div className=\"flex justify-between items-center pt-4 border-t border-gray-100\">\n                    <div className=\"flex gap-3\">\n                      <Link\n                        href={`/edit-tool/${tool._id}`}\n                        className=\"flex items-center justify-center w-10 h-10 bg-blue-100 text-blue-600 rounded-full hover:bg-blue-200 transition-colors duration-200\"\n                        title=\"Edit Tool\"\n                      >\n                        <HiPencilAlt size={18} />\n                      </Link>\n                      <div className=\"flex items-center justify-center w-10 h-10 bg-red-100 text-red-600 rounded-full hover:bg-red-200 transition-colors duration-200\">\n                        <RemoveBtn id={tool._id} />\n                      </div>\n                    </div>\n                    <div className=\"text-xs text-gray-400\">\n                      {tool.createdAt ? new Date(tool.createdAt).toLocaleDateString() : 'Recently added'}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        ) : (\n          <div className=\"text-center py-16\">\n            <div className=\"bg-white rounded-2xl shadow-lg p-12 max-w-md mx-auto\">\n              <div className=\"w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6\">\n                <svg className=\"w-12 h-12 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\" />\n                </svg>\n              </div>\n              <h3 className=\"text-2xl font-bold text-gray-800 mb-4\">No tools available yet</h3>\n              <p className=\"text-gray-600 mb-6\">Be the first to add a tool to the community!</p>\n              <Link\n                href=\"/add-tool\"\n                className=\"inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all duration-200 transform hover:scale-105\"\n              >\n                Add Your First Tool\n              </Link>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;;;AANA;;;;;AA6Ce,SAAS;;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO,CAAC;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,kBAAkB;IAClB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0CAAE,OAAO;YACtC,aAAa;YACb,IAAI;gBACF,MAAM,cAAc,IAAI;gBACxB,OAAO,OAAO,CAAC,cAAc,OAAO;sDAAC,CAAC,CAAC,KAAK,MAAM;wBAChD,IAAI,SAAS,UAAU,OAAO;4BAC5B,YAAY,MAAM,CAAC,KAAK;wBAC1B;oBACF;;gBAEA,MAAM,WAAW,MAAM,MAAM,CAAC,kBAAkB,EAAE,YAAY,QAAQ,IAAI;gBAC1E,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,IAAI,SAAS,EAAE,EAAE;oBACf,SAAS,KAAK,KAAK,IAAI,EAAE;oBACzB,iBAAiB,KAAK,aAAa,IAAI,CAAC;oBACxC,gBAAgB,KAAK,YAAY,IAAI;gBACvC,OAAO;oBACL,QAAQ,KAAK,CAAC,kBAAkB,KAAK,KAAK;oBAC1C,SAAS,EAAE;oBACX,gBAAgB;gBAClB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iBAAiB;gBAC/B,SAAS,EAAE;gBACX,gBAAgB;YAClB,SAAU;gBACR,aAAa;YACf;QACF;yCAAG,EAAE;IAEL,oBAAoB;IACpB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,aAAa;gBACX,OAAO;gBACP,UAAU;gBACV,MAAM;gBACN,cAAc;gBACd,WAAW;gBACX,QAAQ;gBACR,WAAW;YACb;QACF;yBAAG;QAAC;KAAa;IAEjB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;gBAKxD,SAAS,MAAM,MAAM,GAAG,kBACvB,6LAAC;oBAAI,WAAU;8BACZ,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC;4BAAmB,WAAU;;8CAE5B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAyC,KAAK,IAAI;;;;;;sDAChE,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAW,CAAC,2CAA2C,EAC3D,KAAK,MAAM,KAAK,cACZ,gCACA,KAAK,MAAM,KAAK,aAChB,kCACA,2BACJ;0DACC,KAAK,MAAM,EAAE,OAAO,GAAG,gBAAgB,KAAK,MAAM,EAAE,MAAM,MAAM;;;;;;;;;;;;;;;;;8CAMvE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDACV,KAAK,WAAW;;;;;;sDAInB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,+JAAA,CAAA,UAAI;4DACH,MAAM,CAAC,WAAW,EAAE,KAAK,GAAG,EAAE;4DAC9B,WAAU;4DACV,OAAM;sEAEN,cAAA,6LAAC,iJAAA,CAAA,cAAW;gEAAC,MAAM;;;;;;;;;;;sEAErB,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,kIAAA,CAAA,UAAS;gEAAC,IAAI,KAAK,GAAG;;;;;;;;;;;;;;;;;8DAG3B,6LAAC;oDAAI,WAAU;8DACZ,KAAK,SAAS,GAAG,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB,KAAK;;;;;;;;;;;;;;;;;;;2BAtChE,KAAK,GAAG;;;;;;;;;yCA8CtB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;oCAA0B,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACjF,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;0CAGzE,6LAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAClC,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GAtIwB;KAAA", "debugId": null}}]}