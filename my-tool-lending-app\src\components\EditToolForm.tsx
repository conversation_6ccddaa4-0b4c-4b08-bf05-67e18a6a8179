'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';

interface EditToolFormProps {
    id: string;
    name: string;
    description: string;
}

export default function EditToolForm({ id, name, description }: EditToolFormProps) {
    const [newName, setNewName] = useState(name);
    const [newDescription, setNewDescription] = useState(description);
    const router = useRouter();

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        try {
            const res = await fetch(`/api/tools/${id}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ newName, newDescription }),
            });

            if (!res.ok) {
                throw new Error('Failed to update tool');
            }
            
            router.push('/');
            router.refresh();
        } catch (error) {
            console.log(error);
        }
    };

    return (
        <div className="max-w-md mx-auto mt-10">
            <h1 className="text-2xl font-bold mb-4">Edit Tool</h1>
            <form onSubmit={handleSubmit} className="bg-white p-6 rounded-lg shadow-md">
                {/* Form inputs similar to AddToolPage, pre-filled with props */}
                <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700">Tool Name</label>
                    <input
                        onChange={(e) => setNewName(e.target.value)}
                        value={newName}
                        type="text"
                        className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                    />
                </div>
                <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700">Description</label>
                    <textarea
                        onChange={(e) => setNewDescription(e.target.value)}
                        value={newDescription}
                        className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                    />
                </div>
                <button type="submit" className="w-full py-2 px-4 bg-green-600 text-white font-semibold rounded-md hover:bg-green-700">
                    Update Tool
                </button>
            </form>
        </div>
    );
}
