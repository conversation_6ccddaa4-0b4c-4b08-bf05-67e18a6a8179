{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Community%20Tool%20Lending%20Platform/my-tool-lending-app/src/lib/mongodb.ts"], "sourcesContent": ["import mongoose from 'mongoose';\r\n\r\n// Connection state tracking\r\nlet isConnected = false;\r\n\r\nconst connectMongoDB = async () => {\r\n  // If already connected, return early\r\n  if (isConnected && mongoose.connection.readyState === 1) {\r\n    return;\r\n  }\r\n\r\n  try {\r\n    const mongoUri = process.env.MONGODB_URI;\r\n\r\n    if (!mongoUri) {\r\n      throw new Error(\"MONGODB_URI environment variable is not defined\");\r\n    }\r\n\r\n    // Disconnect if there's an existing connection in a bad state\r\n    if (mongoose.connection.readyState !== 0) {\r\n      await mongoose.disconnect();\r\n    }\r\n\r\n    console.log(\"Connecting to MongoDB...\");\r\n\r\n    // Connect with optimized settings\r\n    await mongoose.connect(mongoUri, {\r\n      serverSelectionTimeoutMS: 10000, // 10 seconds\r\n      socketTimeoutMS: 45000, // 45 seconds\r\n      family: 4, // Use IPv4, skip trying IPv6\r\n    });\r\n\r\n    isConnected = true;\r\n    console.log(\"✅ MongoDB connected successfully\");\r\n\r\n  } catch (error) {\r\n    isConnected = false;\r\n    console.error(\"❌ MongoDB connection error:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Function to check connection status\r\nexport const getConnectionStatus = () => {\r\n  return {\r\n    isConnected,\r\n    readyState: mongoose.connection.readyState,\r\n    host: mongoose.connection.host,\r\n    port: mongoose.connection.port,\r\n    databaseName: mongoose.connection.db?.databaseName,\r\n  };\r\n};\r\n\r\n// Function to disconnect\r\nexport const disconnectMongoDB = async () => {\r\n  try {\r\n    await mongoose.disconnect();\r\n    isConnected = false;\r\n    console.log(\"🔌 Disconnected from MongoDB.\");\r\n  } catch (error) {\r\n    console.error(\"❌ Error disconnecting from MongoDB:\", error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport default connectMongoDB;"], "names": [], "mappings": ";;;;;AAAA;;AAEA,4BAA4B;AAC5B,IAAI,cAAc;AAElB,MAAM,iBAAiB;IACrB,qCAAqC;IACrC,IAAI,eAAe,yGAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,UAAU,KAAK,GAAG;QACvD;IACF;IAEA,IAAI;QACF,MAAM,WAAW,QAAQ,GAAG,CAAC,WAAW;QAExC,IAAI,CAAC,UAAU;YACb,MAAM,IAAI,MAAM;QAClB;QAEA,8DAA8D;QAC9D,IAAI,yGAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,UAAU,KAAK,GAAG;YACxC,MAAM,yGAAA,CAAA,UAAQ,CAAC,UAAU;QAC3B;QAEA,QAAQ,GAAG,CAAC;QAEZ,kCAAkC;QAClC,MAAM,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,UAAU;YAC/B,0BAA0B;YAC1B,iBAAiB;YACjB,QAAQ;QACV;QAEA,cAAc;QACd,QAAQ,GAAG,CAAC;IAEd,EAAE,OAAO,OAAO;QACd,cAAc;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,MAAM;IACR;AACF;AAGO,MAAM,sBAAsB;IACjC,OAAO;QACL;QACA,YAAY,yGAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,UAAU;QAC1C,MAAM,yGAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,IAAI;QAC9B,MAAM,yGAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,IAAI;QAC9B,cAAc,yGAAA,CAAA,UAAQ,CAAC,UAAU,CAAC,EAAE,EAAE;IACxC;AACF;AAGO,MAAM,oBAAoB;IAC/B,IAAI;QACF,MAAM,yGAAA,CAAA,UAAQ,CAAC,UAAU;QACzB,cAAc;QACd,QAAQ,GAAG,CAAC;IACd,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,MAAM;IACR;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 124, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Community%20Tool%20Lending%20Platform/my-tool-lending-app/src/models/tool.ts"], "sourcesContent": ["import mongoose, { Schema } from 'mongoose';\r\n\r\nconst toolSchema = new Schema(\r\n  {\r\n    name: String,\r\n    description: String,\r\n    status: {\r\n        type: String,\r\n        default: 'available',\r\n    }\r\n  },\r\n  {\r\n    timestamps: true, // This will automatically add createdAt and updatedAt fields\r\n  }\r\n);\r\n\r\nconst Tool = mongoose.models.Tool || mongoose.model(\"Tool\", toolSchema);\r\n\r\nexport default Tool;"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,aAAa,IAAI,yGAAA,CAAA,SAAM,CAC3B;IACE,MAAM;IACN,aAAa;IACb,QAAQ;QACJ,MAAM;QACN,SAAS;IACb;AACF,GACA;IACE,YAAY;AACd;AAGF,MAAM,OAAO,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,IAAI,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAC,QAAQ;uCAE7C", "debugId": null}}, {"offset": {"line": 155, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Community%20Tool%20Lending%20Platform/my-tool-lending-app/src/app/api/tools/%5Bid%5D/route.ts"], "sourcesContent": ["import connectMongoDB from \"@/lib/mongodb\";\nimport Tool from \"@/models/tool\";\nimport { NextResponse } from \"next/server\";\n\n// GET a single tool by ID\nexport async function GET(request: Request, { params }: { params: { id: string } }) {\n  const { id } = params;\n  await connectMongoDB();\n  const tool = await Tool.findOne({ _id: id });\n  return NextResponse.json({ tool }, { status: 200 });\n}\n\n// UPDATE a tool by ID\nexport async function PUT(request: Request, { params }: { params: { id: string } }) {\n  const { id } = params;\n  const { newName: name, newDescription: description } = await request.json();\n  await connectMongoDB();\n  await Tool.findByIdAndUpdate(id, { name, description });\n  return NextResponse.json({ message: \"Tool updated\" }, { status: 200 });\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAGO,eAAe,IAAI,OAAgB,EAAE,EAAE,MAAM,EAA8B;IAChF,MAAM,EAAE,EAAE,EAAE,GAAG;IACf,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAc,AAAD;IACnB,MAAM,OAAO,MAAM,uHAAA,CAAA,UAAI,CAAC,OAAO,CAAC;QAAE,KAAK;IAAG;IAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QAAE;IAAK,GAAG;QAAE,QAAQ;IAAI;AACnD;AAGO,eAAe,IAAI,OAAgB,EAAE,EAAE,MAAM,EAA8B;IAChF,MAAM,EAAE,EAAE,EAAE,GAAG;IACf,MAAM,EAAE,SAAS,IAAI,EAAE,gBAAgB,WAAW,EAAE,GAAG,MAAM,QAAQ,IAAI;IACzE,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAc,AAAD;IACnB,MAAM,uHAAA,CAAA,UAAI,CAAC,iBAAiB,CAAC,IAAI;QAAE;QAAM;IAAY;IACrD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QAAE,SAAS;IAAe,GAAG;QAAE,QAAQ;IAAI;AACtE", "debugId": null}}]}