{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Community%20Tool%20Lending%20Platform/my-tool-lending-app/src/components/EditToolForm.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\n\ninterface Tool {\n  _id: string;\n  name: string;\n  description: string;\n  category?: string;\n  condition?: string;\n  location?: {\n    city: string;\n    area: string;\n    postalCode?: string;\n  };\n  owner?: {\n    name: string;\n    email: string;\n    phone?: string;\n  };\n  availability?: string;\n  borrowingTerms?: {\n    maxDuration: number;\n    deposit: number;\n    instructions?: string;\n  };\n  tags?: string[];\n}\n\ninterface EditToolFormProps {\n    id: string;\n    name: string;\n    description: string;\n}\n\nexport default function EditToolForm({ id, name, description }: EditToolFormProps) {\n    const [tool, setTool] = useState<Tool | null>(null);\n    const [loading, setLoading] = useState(true);\n\n    // Form states\n    const [newName, setNewName] = useState(name);\n    const [newDescription, setNewDescription] = useState(description);\n    const [category, setCategory] = useState('Other');\n    const [condition, setCondition] = useState('Good');\n    const [city, setCity] = useState('');\n    const [area, setArea] = useState('');\n    const [postalCode, setPostalCode] = useState('');\n    const [ownerName, setOwnerName] = useState('');\n    const [ownerEmail, setOwnerEmail] = useState('');\n    const [ownerPhone, setOwnerPhone] = useState('');\n    const [availability, setAvailability] = useState('available');\n    const [maxDuration, setMaxDuration] = useState(7);\n    const [deposit, setDeposit] = useState(0);\n    const [instructions, setInstructions] = useState('');\n    const [tags, setTags] = useState('');\n\n    const router = useRouter();\n\n    const categories = [\n        'Power Tools', 'Hand Tools', 'Garden Tools', 'Automotive',\n        'Construction', 'Electrical', 'Plumbing', 'Cleaning',\n        'Kitchen Appliances', 'Sports & Recreation', 'Other'\n    ];\n\n    const conditions = ['Excellent', 'Good', 'Fair', 'Poor'];\n    const availabilityOptions = ['available', 'borrowed', 'maintenance', 'unavailable'];\n\n    // Fetch full tool data\n    useEffect(() => {\n        const fetchTool = async () => {\n            try {\n                const response = await fetch(`/api/tools/${id}`);\n                if (response.ok) {\n                    const data = await response.json();\n                    const toolData = data.tool;\n                    setTool(toolData);\n\n                    // Populate form fields\n                    setNewName(toolData.name || '');\n                    setNewDescription(toolData.description || '');\n                    setCategory(toolData.category || 'Other');\n                    setCondition(toolData.condition || 'Good');\n                    setCity(toolData.location?.city || '');\n                    setArea(toolData.location?.area || '');\n                    setPostalCode(toolData.location?.postalCode || '');\n                    setOwnerName(toolData.owner?.name || '');\n                    setOwnerEmail(toolData.owner?.email || '');\n                    setOwnerPhone(toolData.owner?.phone || '');\n                    setAvailability(toolData.availability || toolData.status || 'available');\n                    setMaxDuration(toolData.borrowingTerms?.maxDuration || 7);\n                    setDeposit(toolData.borrowingTerms?.deposit || 0);\n                    setInstructions(toolData.borrowingTerms?.instructions || '');\n                    setTags(toolData.tags?.join(', ') || '');\n                }\n            } catch (error) {\n                console.error('Error fetching tool:', error);\n            } finally {\n                setLoading(false);\n            }\n        };\n\n        fetchTool();\n    }, [id]);\n\n    const handleSubmit = async (e: React.FormEvent) => {\n        e.preventDefault();\n\n        // Validation\n        if (!newName || !newDescription || !city || !area || !ownerName || !ownerEmail) {\n            alert('Please fill in all required fields.');\n            return;\n        }\n\n        const toolData = {\n            name: newName,\n            description: newDescription,\n            category,\n            condition,\n            location: {\n                city,\n                area,\n                postalCode\n            },\n            owner: {\n                name: ownerName,\n                email: ownerEmail,\n                phone: ownerPhone\n            },\n            availability,\n            borrowingTerms: {\n                maxDuration: Number(maxDuration),\n                deposit: Number(deposit),\n                instructions\n            },\n            tags: tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0)\n        };\n\n        try {\n            const res = await fetch(`/api/tools/${id}`, {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json',\n                },\n                body: JSON.stringify(toolData),\n            });\n\n            if (!res.ok) {\n                const errorData = await res.json();\n                throw new Error(errorData.error || 'Failed to update tool');\n            }\n\n            alert('Tool updated successfully!');\n            router.push('/');\n            router.refresh();\n        } catch (error) {\n            console.log(error);\n            alert('Error updating tool: ' + error);\n        }\n    };\n\n    if (loading) {\n        return (\n            <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center\">\n                <div className=\"text-center\">\n                    <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4\"></div>\n                    <p className=\"text-gray-600\">Loading tool data...</p>\n                </div>\n            </div>\n        );\n    }\n\n    return (\n        <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 py-12\">\n            <div className=\"max-w-4xl mx-auto px-4\">\n                {/* Header */}\n                <div className=\"text-center mb-8\">\n                    <h1 className=\"text-4xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent mb-4\">\n                        Edit Tool\n                    </h1>\n                    <p className=\"text-xl text-gray-600\">Update your tool information</p>\n                </div>\n\n                {/* Form */}\n                <div className=\"bg-white/90 backdrop-blur-sm rounded-2xl shadow-xl p-8 border border-white/20\">\n                    <form onSubmit={handleSubmit} className=\"space-y-8\">\n                        {/* Basic Information */}\n                        <div className=\"border-b border-gray-200 pb-8\">\n                            <h3 className=\"text-xl font-bold text-gray-800 mb-6\">Basic Information</h3>\n\n                            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                                <div>\n                                    <label htmlFor=\"name\" className=\"block text-sm font-bold text-gray-700 mb-3\">\n                                        Tool Name *\n                                    </label>\n                                    <input\n                                        id=\"name\"\n                                        type=\"text\"\n                                        onChange={(e) => setNewName(e.target.value)}\n                                        value={newName}\n                                        className=\"w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-300\"\n                                        placeholder=\"e.g., Electric Drill, Lawn Mower\"\n                                        required\n                                    />\n                                </div>\n\n                                <div>\n                                    <label htmlFor=\"category\" className=\"block text-sm font-bold text-gray-700 mb-3\">\n                                        Category *\n                                    </label>\n                                    <select\n                                        id=\"category\"\n                                        onChange={(e) => setCategory(e.target.value)}\n                                        value={category}\n                                        className=\"w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-300\"\n                                        required\n                                    >\n                                        {categories.map(cat => (\n                                            <option key={cat} value={cat}>{cat}</option>\n                                        ))}\n                                    </select>\n                                </div>\n                            </div>\n\n                            <div className=\"mt-6\">\n                                <label htmlFor=\"description\" className=\"block text-sm font-bold text-gray-700 mb-3\">\n                                    Description *\n                                </label>\n                                <textarea\n                                    id=\"description\"\n                                    onChange={(e) => setNewDescription(e.target.value)}\n                                    value={newDescription}\n                                    rows={4}\n                                    className=\"w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-300 resize-none\"\n                                    placeholder=\"Describe your tool, its condition, and any special features...\"\n                                    required\n                                />\n                            </div>\n\n                            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mt-6\">\n                                <div>\n                                    <label htmlFor=\"condition\" className=\"block text-sm font-bold text-gray-700 mb-3\">\n                                        Condition\n                                    </label>\n                                    <select\n                                        id=\"condition\"\n                                        onChange={(e) => setCondition(e.target.value)}\n                                        value={condition}\n                                        className=\"w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-300\"\n                                    >\n                                        {conditions.map(cond => (\n                                            <option key={cond} value={cond}>{cond}</option>\n                                        ))}\n                                    </select>\n                                </div>\n\n                                <div>\n                                    <label htmlFor=\"availability\" className=\"block text-sm font-bold text-gray-700 mb-3\">\n                                        Availability\n                                    </label>\n                                    <select\n                                        id=\"availability\"\n                                        onChange={(e) => setAvailability(e.target.value)}\n                                        value={availability}\n                                        className=\"w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-300\"\n                                    >\n                                        {availabilityOptions.map(option => (\n                                            <option key={option} value={option}>\n                                                {option.charAt(0).toUpperCase() + option.slice(1)}\n                                            </option>\n                                        ))}\n                                    </select>\n                                </div>\n\n                                <div>\n                                    <label htmlFor=\"tags\" className=\"block text-sm font-bold text-gray-700 mb-3\">\n                                        Tags (comma separated)\n                                    </label>\n                                    <input\n                                        id=\"tags\"\n                                        type=\"text\"\n                                        onChange={(e) => setTags(e.target.value)}\n                                        value={tags}\n                                        className=\"w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-300\"\n                                        placeholder=\"e.g., cordless, 18V, battery\"\n                                    />\n                                </div>\n                            </div>\n                        </div>\n\n                        {/* Location Information */}\n                        <div className=\"border-b border-gray-200 pb-8\">\n                            <h3 className=\"text-xl font-bold text-gray-800 mb-6\">Location</h3>\n\n                            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n                                <div>\n                                    <label htmlFor=\"city\" className=\"block text-sm font-bold text-gray-700 mb-3\">\n                                        City *\n                                    </label>\n                                    <input\n                                        id=\"city\"\n                                        type=\"text\"\n                                        onChange={(e) => setCity(e.target.value)}\n                                        value={city}\n                                        className=\"w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-300\"\n                                        placeholder=\"e.g., Colombo\"\n                                        required\n                                    />\n                                </div>\n\n                                <div>\n                                    <label htmlFor=\"area\" className=\"block text-sm font-bold text-gray-700 mb-3\">\n                                        Area/District *\n                                    </label>\n                                    <input\n                                        id=\"area\"\n                                        type=\"text\"\n                                        onChange={(e) => setArea(e.target.value)}\n                                        value={area}\n                                        className=\"w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-300\"\n                                        placeholder=\"e.g., Nugegoda\"\n                                        required\n                                    />\n                                </div>\n\n                                <div>\n                                    <label htmlFor=\"postalCode\" className=\"block text-sm font-bold text-gray-700 mb-3\">\n                                        Postal Code\n                                    </label>\n                                    <input\n                                        id=\"postalCode\"\n                                        type=\"text\"\n                                        onChange={(e) => setPostalCode(e.target.value)}\n                                        value={postalCode}\n                                        className=\"w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-300\"\n                                        placeholder=\"e.g., 10250\"\n                                    />\n                                </div>\n                            </div>\n                        </div>\n\n                        {/* Owner Information */}\n                        <div className=\"border-b border-gray-200 pb-8\">\n                            <h3 className=\"text-xl font-bold text-gray-800 mb-6\">Contact Information</h3>\n\n                            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                                <div>\n                                    <label htmlFor=\"ownerName\" className=\"block text-sm font-bold text-gray-700 mb-3\">\n                                        Your Name *\n                                    </label>\n                                    <input\n                                        id=\"ownerName\"\n                                        type=\"text\"\n                                        onChange={(e) => setOwnerName(e.target.value)}\n                                        value={ownerName}\n                                        className=\"w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-300\"\n                                        placeholder=\"Your full name\"\n                                        required\n                                    />\n                                </div>\n\n                                <div>\n                                    <label htmlFor=\"ownerEmail\" className=\"block text-sm font-bold text-gray-700 mb-3\">\n                                        Email Address *\n                                    </label>\n                                    <input\n                                        id=\"ownerEmail\"\n                                        type=\"email\"\n                                        onChange={(e) => setOwnerEmail(e.target.value)}\n                                        value={ownerEmail}\n                                        className=\"w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-300\"\n                                        placeholder=\"<EMAIL>\"\n                                        required\n                                    />\n                                </div>\n                            </div>\n\n                            <div className=\"mt-6\">\n                                <label htmlFor=\"ownerPhone\" className=\"block text-sm font-bold text-gray-700 mb-3\">\n                                    Phone Number (Optional)\n                                </label>\n                                <input\n                                    id=\"ownerPhone\"\n                                    type=\"tel\"\n                                    onChange={(e) => setOwnerPhone(e.target.value)}\n                                    value={ownerPhone}\n                                    className=\"w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-300\"\n                                    placeholder=\"+94 77 123 4567\"\n                                />\n                            </div>\n                        </div>\n\n                        {/* Borrowing Terms */}\n                        <div className=\"pb-8\">\n                            <h3 className=\"text-xl font-bold text-gray-800 mb-6\">Borrowing Terms</h3>\n\n                            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                                <div>\n                                    <label htmlFor=\"maxDuration\" className=\"block text-sm font-bold text-gray-700 mb-3\">\n                                        Maximum Loan Duration (days)\n                                    </label>\n                                    <input\n                                        id=\"maxDuration\"\n                                        type=\"number\"\n                                        min=\"1\"\n                                        max=\"30\"\n                                        onChange={(e) => setMaxDuration(Number(e.target.value))}\n                                        value={maxDuration}\n                                        className=\"w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-300\"\n                                    />\n                                </div>\n\n                                <div>\n                                    <label htmlFor=\"deposit\" className=\"block text-sm font-bold text-gray-700 mb-3\">\n                                        Security Deposit (LKR)\n                                    </label>\n                                    <input\n                                        id=\"deposit\"\n                                        type=\"number\"\n                                        min=\"0\"\n                                        onChange={(e) => setDeposit(Number(e.target.value))}\n                                        value={deposit}\n                                        className=\"w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-300\"\n                                        placeholder=\"0\"\n                                    />\n                                </div>\n                            </div>\n\n                            <div className=\"mt-6\">\n                                <label htmlFor=\"instructions\" className=\"block text-sm font-bold text-gray-700 mb-3\">\n                                    Special Instructions\n                                </label>\n                                <textarea\n                                    id=\"instructions\"\n                                    onChange={(e) => setInstructions(e.target.value)}\n                                    value={instructions}\n                                    rows={3}\n                                    className=\"w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-4 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-300 resize-none\"\n                                    placeholder=\"Any special care instructions, pickup arrangements, etc.\"\n                                />\n                            </div>\n                        </div>\n\n                        {/* Action Buttons */}\n                        <div className=\"flex flex-col sm:flex-row gap-4 pt-6\">\n                            <button\n                                type=\"submit\"\n                                className=\"flex-1 py-4 px-8 bg-gradient-to-r from-green-500 to-emerald-600 text-white font-bold rounded-xl hover:from-green-600 hover:to-emerald-700 focus:outline-none focus:ring-4 focus:ring-green-500/20 transition-all duration-300 transform hover:scale-105 shadow-lg\"\n                            >\n                                Update Tool\n                            </button>\n                            <button\n                                type=\"button\"\n                                onClick={() => router.push('/')}\n                                className=\"flex-1 py-4 px-8 bg-gradient-to-r from-gray-500 to-gray-600 text-white font-bold rounded-xl hover:from-gray-600 hover:to-gray-700 focus:outline-none focus:ring-4 focus:ring-gray-500/20 transition-all duration-300 transform hover:scale-105 shadow-lg\"\n                            >\n                                Cancel\n                            </button>\n                        </div>\n                    </form>\n\n                    {/* Help Text */}\n                    <div className=\"mt-8 p-6 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl border border-blue-100\">\n                        <p className=\"text-sm text-blue-800\">\n                            <strong>💡 Tip:</strong> Keep your tool information up to date to help community members find exactly what they need. Include any recent changes in condition or availability.\n                        </p>\n                    </div>\n                </div>\n            </div>\n        </div>\n    );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAoCe,SAAS,aAAa,EAAE,EAAE,EAAE,IAAI,EAAE,WAAW,EAAqB;IAC7E,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,cAAc;IACd,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,aAAa;QACf;QAAe;QAAc;QAAgB;QAC7C;QAAgB;QAAc;QAAY;QAC1C;QAAsB;QAAuB;KAChD;IAED,MAAM,aAAa;QAAC;QAAa;QAAQ;QAAQ;KAAO;IACxD,MAAM,sBAAsB;QAAC;QAAa;QAAY;QAAe;KAAc;IAEnF,uBAAuB;IACvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,MAAM,YAAY;YACd,IAAI;gBACA,MAAM,WAAW,MAAM,MAAM,CAAC,WAAW,EAAE,IAAI;gBAC/C,IAAI,SAAS,EAAE,EAAE;oBACb,MAAM,OAAO,MAAM,SAAS,IAAI;oBAChC,MAAM,WAAW,KAAK,IAAI;oBAC1B,QAAQ;oBAER,uBAAuB;oBACvB,WAAW,SAAS,IAAI,IAAI;oBAC5B,kBAAkB,SAAS,WAAW,IAAI;oBAC1C,YAAY,SAAS,QAAQ,IAAI;oBACjC,aAAa,SAAS,SAAS,IAAI;oBACnC,QAAQ,SAAS,QAAQ,EAAE,QAAQ;oBACnC,QAAQ,SAAS,QAAQ,EAAE,QAAQ;oBACnC,cAAc,SAAS,QAAQ,EAAE,cAAc;oBAC/C,aAAa,SAAS,KAAK,EAAE,QAAQ;oBACrC,cAAc,SAAS,KAAK,EAAE,SAAS;oBACvC,cAAc,SAAS,KAAK,EAAE,SAAS;oBACvC,gBAAgB,SAAS,YAAY,IAAI,SAAS,MAAM,IAAI;oBAC5D,eAAe,SAAS,cAAc,EAAE,eAAe;oBACvD,WAAW,SAAS,cAAc,EAAE,WAAW;oBAC/C,gBAAgB,SAAS,cAAc,EAAE,gBAAgB;oBACzD,QAAQ,SAAS,IAAI,EAAE,KAAK,SAAS;gBACzC;YACJ,EAAE,OAAO,OAAO;gBACZ,QAAQ,KAAK,CAAC,wBAAwB;YAC1C,SAAU;gBACN,WAAW;YACf;QACJ;QAEA;IACJ,GAAG;QAAC;KAAG;IAEP,MAAM,eAAe,OAAO;QACxB,EAAE,cAAc;QAEhB,aAAa;QACb,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,QAAQ,CAAC,QAAQ,CAAC,aAAa,CAAC,YAAY;YAC5E,MAAM;YACN;QACJ;QAEA,MAAM,WAAW;YACb,MAAM;YACN,aAAa;YACb;YACA;YACA,UAAU;gBACN;gBACA;gBACA;YACJ;YACA,OAAO;gBACH,MAAM;gBACN,OAAO;gBACP,OAAO;YACX;YACA;YACA,gBAAgB;gBACZ,aAAa,OAAO;gBACpB,SAAS,OAAO;gBAChB;YACJ;YACA,MAAM,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,MAAO,IAAI,IAAI,IAAI,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,GAAG;QAC5E;QAEA,IAAI;YACA,MAAM,MAAM,MAAM,MAAM,CAAC,WAAW,EAAE,IAAI,EAAE;gBACxC,QAAQ;gBACR,SAAS;oBACL,gBAAgB;gBACpB;gBACA,MAAM,KAAK,SAAS,CAAC;YACzB;YAEA,IAAI,CAAC,IAAI,EAAE,EAAE;gBACT,MAAM,YAAY,MAAM,IAAI,IAAI;gBAChC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI;YACvC;YAEA,MAAM;YACN,OAAO,IAAI,CAAC;YACZ,OAAO,OAAO;QAClB,EAAE,OAAO,OAAO;YACZ,QAAQ,GAAG,CAAC;YACZ,MAAM,0BAA0B;QACpC;IACJ;IAEA,IAAI,SAAS;QACT,qBACI,8OAAC;YAAI,WAAU;sBACX,cAAA,8OAAC;gBAAI,WAAU;;kCACX,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAI7C;IAEA,qBACI,8OAAC;QAAI,WAAU;kBACX,cAAA,8OAAC;YAAI,WAAU;;8BAEX,8OAAC;oBAAI,WAAU;;sCACX,8OAAC;4BAAG,WAAU;sCAAoH;;;;;;sCAGlI,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;8BAIzC,8OAAC;oBAAI,WAAU;;sCACX,8OAAC;4BAAK,UAAU;4BAAc,WAAU;;8CAEpC,8OAAC;oCAAI,WAAU;;sDACX,8OAAC;4CAAG,WAAU;sDAAuC;;;;;;sDAErD,8OAAC;4CAAI,WAAU;;8DACX,8OAAC;;sEACG,8OAAC;4DAAM,SAAQ;4DAAO,WAAU;sEAA6C;;;;;;sEAG7E,8OAAC;4DACG,IAAG;4DACH,MAAK;4DACL,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;4DAC1C,OAAO;4DACP,WAAU;4DACV,aAAY;4DACZ,QAAQ;;;;;;;;;;;;8DAIhB,8OAAC;;sEACG,8OAAC;4DAAM,SAAQ;4DAAW,WAAU;sEAA6C;;;;;;sEAGjF,8OAAC;4DACG,IAAG;4DACH,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;4DAC3C,OAAO;4DACP,WAAU;4DACV,QAAQ;sEAEP,WAAW,GAAG,CAAC,CAAA,oBACZ,8OAAC;oEAAiB,OAAO;8EAAM;mEAAlB;;;;;;;;;;;;;;;;;;;;;;sDAM7B,8OAAC;4CAAI,WAAU;;8DACX,8OAAC;oDAAM,SAAQ;oDAAc,WAAU;8DAA6C;;;;;;8DAGpF,8OAAC;oDACG,IAAG;oDACH,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;oDACjD,OAAO;oDACP,MAAM;oDACN,WAAU;oDACV,aAAY;oDACZ,QAAQ;;;;;;;;;;;;sDAIhB,8OAAC;4CAAI,WAAU;;8DACX,8OAAC;;sEACG,8OAAC;4DAAM,SAAQ;4DAAY,WAAU;sEAA6C;;;;;;sEAGlF,8OAAC;4DACG,IAAG;4DACH,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;4DAC5C,OAAO;4DACP,WAAU;sEAET,WAAW,GAAG,CAAC,CAAA,qBACZ,8OAAC;oEAAkB,OAAO;8EAAO;mEAApB;;;;;;;;;;;;;;;;8DAKzB,8OAAC;;sEACG,8OAAC;4DAAM,SAAQ;4DAAe,WAAU;sEAA6C;;;;;;sEAGrF,8OAAC;4DACG,IAAG;4DACH,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;4DAC/C,OAAO;4DACP,WAAU;sEAET,oBAAoB,GAAG,CAAC,CAAA,uBACrB,8OAAC;oEAAoB,OAAO;8EACvB,OAAO,MAAM,CAAC,GAAG,WAAW,KAAK,OAAO,KAAK,CAAC;mEADtC;;;;;;;;;;;;;;;;8DAOzB,8OAAC;;sEACG,8OAAC;4DAAM,SAAQ;4DAAO,WAAU;sEAA6C;;;;;;sEAG7E,8OAAC;4DACG,IAAG;4DACH,MAAK;4DACL,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;4DACvC,OAAO;4DACP,WAAU;4DACV,aAAY;;;;;;;;;;;;;;;;;;;;;;;;8CAO5B,8OAAC;oCAAI,WAAU;;sDACX,8OAAC;4CAAG,WAAU;sDAAuC;;;;;;sDAErD,8OAAC;4CAAI,WAAU;;8DACX,8OAAC;;sEACG,8OAAC;4DAAM,SAAQ;4DAAO,WAAU;sEAA6C;;;;;;sEAG7E,8OAAC;4DACG,IAAG;4DACH,MAAK;4DACL,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;4DACvC,OAAO;4DACP,WAAU;4DACV,aAAY;4DACZ,QAAQ;;;;;;;;;;;;8DAIhB,8OAAC;;sEACG,8OAAC;4DAAM,SAAQ;4DAAO,WAAU;sEAA6C;;;;;;sEAG7E,8OAAC;4DACG,IAAG;4DACH,MAAK;4DACL,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;4DACvC,OAAO;4DACP,WAAU;4DACV,aAAY;4DACZ,QAAQ;;;;;;;;;;;;8DAIhB,8OAAC;;sEACG,8OAAC;4DAAM,SAAQ;4DAAa,WAAU;sEAA6C;;;;;;sEAGnF,8OAAC;4DACG,IAAG;4DACH,MAAK;4DACL,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4DAC7C,OAAO;4DACP,WAAU;4DACV,aAAY;;;;;;;;;;;;;;;;;;;;;;;;8CAO5B,8OAAC;oCAAI,WAAU;;sDACX,8OAAC;4CAAG,WAAU;sDAAuC;;;;;;sDAErD,8OAAC;4CAAI,WAAU;;8DACX,8OAAC;;sEACG,8OAAC;4DAAM,SAAQ;4DAAY,WAAU;sEAA6C;;;;;;sEAGlF,8OAAC;4DACG,IAAG;4DACH,MAAK;4DACL,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;4DAC5C,OAAO;4DACP,WAAU;4DACV,aAAY;4DACZ,QAAQ;;;;;;;;;;;;8DAIhB,8OAAC;;sEACG,8OAAC;4DAAM,SAAQ;4DAAa,WAAU;sEAA6C;;;;;;sEAGnF,8OAAC;4DACG,IAAG;4DACH,MAAK;4DACL,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4DAC7C,OAAO;4DACP,WAAU;4DACV,aAAY;4DACZ,QAAQ;;;;;;;;;;;;;;;;;;sDAKpB,8OAAC;4CAAI,WAAU;;8DACX,8OAAC;oDAAM,SAAQ;oDAAa,WAAU;8DAA6C;;;;;;8DAGnF,8OAAC;oDACG,IAAG;oDACH,MAAK;oDACL,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oDAC7C,OAAO;oDACP,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;8CAMxB,8OAAC;oCAAI,WAAU;;sDACX,8OAAC;4CAAG,WAAU;sDAAuC;;;;;;sDAErD,8OAAC;4CAAI,WAAU;;8DACX,8OAAC;;sEACG,8OAAC;4DAAM,SAAQ;4DAAc,WAAU;sEAA6C;;;;;;sEAGpF,8OAAC;4DACG,IAAG;4DACH,MAAK;4DACL,KAAI;4DACJ,KAAI;4DACJ,UAAU,CAAC,IAAM,eAAe,OAAO,EAAE,MAAM,CAAC,KAAK;4DACrD,OAAO;4DACP,WAAU;;;;;;;;;;;;8DAIlB,8OAAC;;sEACG,8OAAC;4DAAM,SAAQ;4DAAU,WAAU;sEAA6C;;;;;;sEAGhF,8OAAC;4DACG,IAAG;4DACH,MAAK;4DACL,KAAI;4DACJ,UAAU,CAAC,IAAM,WAAW,OAAO,EAAE,MAAM,CAAC,KAAK;4DACjD,OAAO;4DACP,WAAU;4DACV,aAAY;;;;;;;;;;;;;;;;;;sDAKxB,8OAAC;4CAAI,WAAU;;8DACX,8OAAC;oDAAM,SAAQ;oDAAe,WAAU;8DAA6C;;;;;;8DAGrF,8OAAC;oDACG,IAAG;oDACH,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;oDAC/C,OAAO;oDACP,MAAM;oDACN,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;8CAMxB,8OAAC;oCAAI,WAAU;;sDACX,8OAAC;4CACG,MAAK;4CACL,WAAU;sDACb;;;;;;sDAGD,8OAAC;4CACG,MAAK;4CACL,SAAS,IAAM,OAAO,IAAI,CAAC;4CAC3B,WAAU;sDACb;;;;;;;;;;;;;;;;;;sCAOT,8OAAC;4BAAI,WAAU;sCACX,cAAA,8OAAC;gCAAE,WAAU;;kDACT,8OAAC;kDAAO;;;;;;oCAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpD", "debugId": null}}, {"offset": {"line": 877, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Community%20Tool%20Lending%20Platform/my-tool-lending-app/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 884, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Community%20Tool%20Lending%20Platform/my-tool-lending-app/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}