{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Community%20Tool%20Lending%20Platform/my-tool-lending-app/src/components/EditToolForm.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/navigation';\n\ninterface EditToolFormProps {\n    id: string;\n    name: string;\n    description: string;\n}\n\nexport default function EditToolForm({ id, name, description }: EditToolFormProps) {\n    const [newName, setNewName] = useState(name);\n    const [newDescription, setNewDescription] = useState(description);\n    const router = useRouter();\n\n    const handleSubmit = async (e: React.FormEvent) => {\n        e.preventDefault();\n\n        try {\n            const res = await fetch(`/api/tools/${id}`, {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json',\n                },\n                body: JSON.stringify({ newName, newDescription }),\n            });\n\n            if (!res.ok) {\n                throw new Error('Failed to update tool');\n            }\n            \n            router.push('/');\n            router.refresh();\n        } catch (error) {\n            console.log(error);\n        }\n    };\n\n    return (\n        <div className=\"max-w-md mx-auto mt-10\">\n            <h1 className=\"text-2xl font-bold mb-4\">Edit Tool</h1>\n            <form onSubmit={handleSubmit} className=\"bg-white p-6 rounded-lg shadow-md\">\n                {/* Form inputs similar to AddToolPage, pre-filled with props */}\n                <div className=\"mb-4\">\n                    <label className=\"block text-sm font-medium text-gray-700\">Tool Name</label>\n                    <input\n                        onChange={(e) => setNewName(e.target.value)}\n                        value={newName}\n                        type=\"text\"\n                        className=\"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\"\n                    />\n                </div>\n                <div className=\"mb-4\">\n                    <label className=\"block text-sm font-medium text-gray-700\">Description</label>\n                    <textarea\n                        onChange={(e) => setNewDescription(e.target.value)}\n                        value={newDescription}\n                        className=\"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\"\n                    />\n                </div>\n                <button type=\"submit\" className=\"w-full py-2 px-4 bg-green-600 text-white font-semibold rounded-md hover:bg-green-700\">\n                    Update Tool\n                </button>\n            </form>\n        </div>\n    );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAWe,SAAS,aAAa,EAAE,EAAE,EAAE,IAAI,EAAE,WAAW,EAAqB;IAC7E,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe,OAAO;QACxB,EAAE,cAAc;QAEhB,IAAI;YACA,MAAM,MAAM,MAAM,MAAM,CAAC,WAAW,EAAE,IAAI,EAAE;gBACxC,QAAQ;gBACR,SAAS;oBACL,gBAAgB;gBACpB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAS;gBAAe;YACnD;YAEA,IAAI,CAAC,IAAI,EAAE,EAAE;gBACT,MAAM,IAAI,MAAM;YACpB;YAEA,OAAO,IAAI,CAAC;YACZ,OAAO,OAAO;QAClB,EAAE,OAAO,OAAO;YACZ,QAAQ,GAAG,CAAC;QAChB;IACJ;IAEA,qBACI,8OAAC;QAAI,WAAU;;0BACX,8OAAC;gBAAG,WAAU;0BAA0B;;;;;;0BACxC,8OAAC;gBAAK,UAAU;gBAAc,WAAU;;kCAEpC,8OAAC;wBAAI,WAAU;;0CACX,8OAAC;gCAAM,WAAU;0CAA0C;;;;;;0CAC3D,8OAAC;gCACG,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;gCAC1C,OAAO;gCACP,MAAK;gCACL,WAAU;;;;;;;;;;;;kCAGlB,8OAAC;wBAAI,WAAU;;0CACX,8OAAC;gCAAM,WAAU;0CAA0C;;;;;;0CAC3D,8OAAC;gCACG,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;gCACjD,OAAO;gCACP,WAAU;;;;;;;;;;;;kCAGlB,8OAAC;wBAAO,MAAK;wBAAS,WAAU;kCAAuF;;;;;;;;;;;;;;;;;;AAMvI", "debugId": null}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Community%20Tool%20Lending%20Platform/my-tool-lending-app/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 145, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Community%20Tool%20Lending%20Platform/my-tool-lending-app/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}