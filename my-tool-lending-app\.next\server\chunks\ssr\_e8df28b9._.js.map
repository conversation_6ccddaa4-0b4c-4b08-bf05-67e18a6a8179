{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Community%20Tool%20Lending%20Platform/my-tool-lending-app/src/app/debug/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\nexport default function DebugPage() {\n  const [tools, setTools] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [testName, setTestName] = useState('Test Tool');\n  const [testDescription, setTestDescription] = useState('This is a test tool for debugging');\n\n  const fetchTools = async () => {\n    setLoading(true);\n    setError('');\n    try {\n      const response = await fetch('/api/tools');\n      const data = await response.json();\n      \n      if (response.ok) {\n        setTools(data.tools || []);\n      } else {\n        setError(`Failed to fetch: ${data.error || 'Unknown error'}`);\n      }\n    } catch (err) {\n      setError(`Network error: ${err}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const createTestTool = async () => {\n    setLoading(true);\n    setError('');\n    try {\n      const response = await fetch('/api/tools', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          name: testName,\n          description: testDescription,\n        }),\n      });\n      \n      const data = await response.json();\n      \n      if (response.ok) {\n        alert('Tool created successfully!');\n        fetchTools(); // Refresh the list\n      } else {\n        setError(`Failed to create: ${data.error || 'Unknown error'}`);\n      }\n    } catch (err) {\n      setError(`Network error: ${err}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchTools();\n  }, []);\n\n  return (\n    <div className=\"container mx-auto p-6\">\n      <h1 className=\"text-3xl font-bold mb-6\">Database Debug Page</h1>\n      \n      {/* Test Tool Creation */}\n      <div className=\"bg-white p-6 rounded-lg shadow mb-6\">\n        <h2 className=\"text-xl font-semibold mb-4\">Test Tool Creation</h2>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4\">\n          <input\n            type=\"text\"\n            value={testName}\n            onChange={(e) => setTestName(e.target.value)}\n            placeholder=\"Tool name\"\n            className=\"border p-2 rounded\"\n          />\n          <input\n            type=\"text\"\n            value={testDescription}\n            onChange={(e) => setTestDescription(e.target.value)}\n            placeholder=\"Tool description\"\n            className=\"border p-2 rounded\"\n          />\n        </div>\n        <button\n          onClick={createTestTool}\n          disabled={loading}\n          className=\"bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:opacity-50\"\n        >\n          {loading ? 'Creating...' : 'Create Test Tool'}\n        </button>\n      </div>\n\n      {/* Refresh Button */}\n      <div className=\"mb-6\">\n        <button\n          onClick={fetchTools}\n          disabled={loading}\n          className=\"bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 disabled:opacity-50\"\n        >\n          {loading ? 'Loading...' : 'Refresh Tools'}\n        </button>\n      </div>\n\n      {/* Error Display */}\n      {error && (\n        <div className=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6\">\n          <strong>Error:</strong> {error}\n        </div>\n      )}\n\n      {/* Tools Display */}\n      <div className=\"bg-white p-6 rounded-lg shadow\">\n        <h2 className=\"text-xl font-semibold mb-4\">Tools in Database ({tools.length})</h2>\n        \n        {loading ? (\n          <p>Loading...</p>\n        ) : tools.length > 0 ? (\n          <div className=\"space-y-4\">\n            {tools.map((tool: any, index) => (\n              <div key={tool._id || index} className=\"border p-4 rounded\">\n                <h3 className=\"font-semibold\">{tool.name}</h3>\n                <p className=\"text-gray-600\">{tool.description}</p>\n                <p className=\"text-sm text-gray-500\">\n                  Status: {tool.status} | ID: {tool._id}\n                </p>\n                {tool.createdAt && (\n                  <p className=\"text-xs text-gray-400\">\n                    Created: {new Date(tool.createdAt).toLocaleString()}\n                  </p>\n                )}\n              </div>\n            ))}\n          </div>\n        ) : (\n          <p className=\"text-gray-500\">No tools found in database</p>\n        )}\n      </div>\n\n      {/* Environment Info */}\n      <div className=\"bg-gray-100 p-4 rounded mt-6\">\n        <h3 className=\"font-semibold mb-2\">Environment Info</h3>\n        <p className=\"text-sm\">\n          MongoDB URI: {process.env.MONGODB_URI ? 'Configured ✅' : 'Missing ❌'}\n        </p>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,MAAM,aAAa;QACjB,WAAW;QACX,SAAS;QACT,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,SAAS,KAAK,KAAK,IAAI,EAAE;YAC3B,OAAO;gBACL,SAAS,CAAC,iBAAiB,EAAE,KAAK,KAAK,IAAI,iBAAiB;YAC9D;QACF,EAAE,OAAO,KAAK;YACZ,SAAS,CAAC,eAAe,EAAE,KAAK;QAClC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB;QACrB,WAAW;QACX,SAAS;QACT,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,cAAc;gBACzC,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,MAAM;oBACN,aAAa;gBACf;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM;gBACN,cAAc,mBAAmB;YACnC,OAAO;gBACL,SAAS,CAAC,kBAAkB,EAAE,KAAK,KAAK,IAAI,iBAAiB;YAC/D;QACF,EAAE,OAAO,KAAK;YACZ,SAAS,CAAC,eAAe,EAAE,KAAK;QAClC,SAAU;YACR,WAAW;QACb;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAA0B;;;;;;0BAGxC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAC3C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,MAAK;gCACL,OAAO;gCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;gCAC3C,aAAY;gCACZ,WAAU;;;;;;0CAEZ,8OAAC;gCACC,MAAK;gCACL,OAAO;gCACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;gCAClD,aAAY;gCACZ,WAAU;;;;;;;;;;;;kCAGd,8OAAC;wBACC,SAAS;wBACT,UAAU;wBACV,WAAU;kCAET,UAAU,gBAAgB;;;;;;;;;;;;0BAK/B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,SAAS;oBACT,UAAU;oBACV,WAAU;8BAET,UAAU,eAAe;;;;;;;;;;;YAK7B,uBACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;kCAAO;;;;;;oBAAe;oBAAE;;;;;;;0BAK7B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;4BAA6B;4BAAoB,MAAM,MAAM;4BAAC;;;;;;;oBAE3E,wBACC,8OAAC;kCAAE;;;;;+BACD,MAAM,MAAM,GAAG,kBACjB,8OAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC,MAAW,sBACrB,8OAAC;gCAA4B,WAAU;;kDACrC,8OAAC;wCAAG,WAAU;kDAAiB,KAAK,IAAI;;;;;;kDACxC,8OAAC;wCAAE,WAAU;kDAAiB,KAAK,WAAW;;;;;;kDAC9C,8OAAC;wCAAE,WAAU;;4CAAwB;4CAC1B,KAAK,MAAM;4CAAC;4CAAQ,KAAK,GAAG;;;;;;;oCAEtC,KAAK,SAAS,kBACb,8OAAC;wCAAE,WAAU;;4CAAwB;4CACzB,IAAI,KAAK,KAAK,SAAS,EAAE,cAAc;;;;;;;;+BAR7C,KAAK,GAAG,IAAI;;;;;;;;;6CAe1B,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;0BAKjC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAqB;;;;;;kCACnC,8OAAC;wBAAE,WAAU;;4BAAU;4BACP,QAAQ,GAAG,CAAC,WAAW,GAAG,iBAAiB;;;;;;;;;;;;;;;;;;;AAKnE", "debugId": null}}, {"offset": {"line": 302, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Community%20Tool%20Lending%20Platform/my-tool-lending-app/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}]}