import mongoose from 'mongoose';

// Connection state tracking
let isConnected = false;

const connectMongoDB = async () => {
  // If already connected, return early
  if (isConnected) {
    console.log("Already connected to MongoDB.");
    return;
  }

  try {
    const mongoUri = process.env.MONGODB_URI;

    if (!mongoUri) {
      throw new Error("MONGODB_URI environment variable is not defined");
    }

    console.log("Attempting to connect to MongoDB...");

    // Connect with additional options for better error handling
    await mongoose.connect(mongoUri);

    isConnected = true;
    console.log("✅ Successfully connected to MongoDB.");

    // Log connection details (without sensitive info)
    const connection = mongoose.connection;
    console.log(`📊 Database: ${connection.db?.databaseName}`);
    console.log(`🔗 Host: ${connection.host}:${connection.port}`);
    console.log(`📈 Ready State: ${connection.readyState}`);

  } catch (error) {
    isConnected = false;
    console.error("❌ Error connecting to MongoDB:", error);
    throw error;
  }
};

// Function to check connection status
export const getConnectionStatus = () => {
  return {
    isConnected,
    readyState: mongoose.connection.readyState,
    host: mongoose.connection.host,
    port: mongoose.connection.port,
    databaseName: mongoose.connection.db?.databaseName,
  };
};

// Function to disconnect
export const disconnectMongoDB = async () => {
  try {
    await mongoose.disconnect();
    isConnected = false;
    console.log("🔌 Disconnected from MongoDB.");
  } catch (error) {
    console.error("❌ Error disconnecting from MongoDB:", error);
    throw error;
  }
};

export default connectMongoDB;