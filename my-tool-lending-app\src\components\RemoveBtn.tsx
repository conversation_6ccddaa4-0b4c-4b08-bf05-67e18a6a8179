'use client';

import { HiOutlineTrash } from 'react-icons/hi';
import { useRouter } from 'next/navigation';

export default function RemoveBtn({ id }: { id: string }) {
    const router = useRouter();
    const removeTool = async () => {
        const confirmed = confirm('Are you sure you want to delete this tool?');

        if (confirmed) {
            const res = await fetch(`/api/tools?id=${id}`, {
                method: 'DELETE',
            });
            
            if (res.ok) {
                router.refresh();
            }
        }
    };

    return (
        <button onClick={removeTool} className="text-red-400 hover:text-red-600">
            <HiOutlineTrash size={24} />
        </button>
    );
}
