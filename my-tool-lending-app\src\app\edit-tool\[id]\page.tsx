import EditToolForm from "@/components/EditToolForm";

const getToolById = async (id: string) => {
    try {
        const res = await fetch(`http://localhost:3000/api/tools/${id}`, {
            cache: 'no-store',
        });
        if (!res.ok) {
            throw new Error("Failed to fetch tool");
        }
        return res.json();
    } catch (error) {
        console.log(error);
    }
};

export default async function EditTool({ params }: { params: Promise<{ id: string }> }) {
    const { id } = await params;
    const { tool } = await getToolById(id);
    const { name, description } = tool;

    return <EditToolForm id={id} name={name} description={description} />;
}
