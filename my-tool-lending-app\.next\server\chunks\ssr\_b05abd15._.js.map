{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Community%20Tool%20Lending%20Platform/my-tool-lending-app/src/components/RemoveBtn.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/RemoveBtn.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/RemoveBtn.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgS,GAC7T,8DACA", "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Community%20Tool%20Lending%20Platform/my-tool-lending-app/src/components/RemoveBtn.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/RemoveBtn.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/RemoveBtn.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA4Q,GACzS,0CACA", "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Community%20Tool%20Lending%20Platform/my-tool-lending-app/src/app/page.tsx"], "sourcesContent": ["import Link from 'next/link';\nimport RemoveBtn from '@/components/RemoveBtn';\nimport { HiPencilAlt } from 'react-icons/hi';\n\nconst getTools = async () => {\n  try {\n    const res = await fetch('http://localhost:3000/api/tools', {\n      cache: 'no-store',\n    });\n    if (!res.ok) {\n      throw new Error(\"Failed to fetch tools\");\n    }\n    return res.json();\n  } catch (error) {\n    console.log(\"Error loading tools: \", error);\n    return { tools: [] }; // Return empty array on error\n  }\n};\n\nexport default async function Home() {\n  const data = await getTools();\n  const tools = data?.tools || [];\n\n  return (\n    <div className=\"container mx-auto p-4\">\n      <h1 className=\"text-3xl font-bold mb-6\">Community Tools</h1>\n\n      {tools && tools.length > 0 ? (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n          {tools.map((tool: any) => (\n            <div key={tool._id} className=\"p-4 border rounded-lg shadow flex flex-col justify-between\">\n              <div>\n                <h2 className=\"text-xl font-semibold\">{tool.name}</h2>\n                <p className=\"text-gray-600 mt-2\">{tool.description}</p>\n                <p className=\"text-sm text-gray-500 mt-2\">Status: {tool.status}</p>\n              </div>\n              <div className=\"flex gap-2 mt-4\">\n                <RemoveBtn id={tool._id} />\n                <Link href={`/edit-tool/${tool._id}`} className=\"text-blue-400 hover:text-blue-600\">\n                  <HiPencilAlt size={24} />\n                </Link>\n              </div>\n            </div>\n          ))}\n        </div>\n      ) : (\n        <div className=\"text-center py-8\">\n          <p className=\"text-gray-500 text-lg\">No tools available yet.</p>\n          <p className=\"text-gray-400\">Be the first to add a tool to the community!</p>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,WAAW;IACf,IAAI;QACF,MAAM,MAAM,MAAM,MAAM,mCAAmC;YACzD,OAAO;QACT;QACA,IAAI,CAAC,IAAI,EAAE,EAAE;YACX,MAAM,IAAI,MAAM;QAClB;QACA,OAAO,IAAI,IAAI;IACjB,EAAE,OAAO,OAAO;QACd,QAAQ,GAAG,CAAC,yBAAyB;QACrC,OAAO;YAAE,OAAO,EAAE;QAAC,GAAG,8BAA8B;IACtD;AACF;AAEe,eAAe;IAC5B,MAAM,OAAO,MAAM;IACnB,MAAM,QAAQ,MAAM,SAAS,EAAE;IAE/B,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAA0B;;;;;;YAEvC,SAAS,MAAM,MAAM,GAAG,kBACvB,8OAAC;gBAAI,WAAU;0BACZ,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;wBAAmB,WAAU;;0CAC5B,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAyB,KAAK,IAAI;;;;;;kDAChD,8OAAC;wCAAE,WAAU;kDAAsB,KAAK,WAAW;;;;;;kDACnD,8OAAC;wCAAE,WAAU;;4CAA6B;4CAAS,KAAK,MAAM;;;;;;;;;;;;;0CAEhE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,+HAAA,CAAA,UAAS;wCAAC,IAAI,KAAK,GAAG;;;;;;kDACvB,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAM,CAAC,WAAW,EAAE,KAAK,GAAG,EAAE;wCAAE,WAAU;kDAC9C,cAAA,8OAAC,8IAAA,CAAA,cAAW;4CAAC,MAAM;;;;;;;;;;;;;;;;;;uBATf,KAAK,GAAG;;;;;;;;;qCAgBtB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;kCACrC,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;;AAKvC", "debugId": null}}]}