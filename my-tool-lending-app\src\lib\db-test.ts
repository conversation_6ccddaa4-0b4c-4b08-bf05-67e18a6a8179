import mongoose from 'mongoose';
import connectMongoDB, { getConnectionStatus, disconnectMongoDB } from './mongodb';
import Tool from '../models/tool';

export interface DatabaseTestResult {
  success: boolean;
  message: string;
  details?: any;
  timestamp: Date;
}

export class DatabaseTester {
  private results: DatabaseTestResult[] = [];

  private addResult(success: boolean, message: string, details?: any): void {
    this.results.push({
      success,
      message,
      details,
      timestamp: new Date()
    });
  }

  async testConnection(): Promise<DatabaseTestResult> {
    console.log("\n🔍 Testing MongoDB Connection...");
    
    try {
      await connectMongoDB();
      const status = getConnectionStatus();
      
      if (status.isConnected && status.readyState === 1) {
        const result = {
          success: true,
          message: "MongoDB connection successful",
          details: status,
          timestamp: new Date()
        };
        this.addResult(true, "Connection test passed", status);
        console.log("✅ Connection test: PASSED");
        return result;
      } else {
        const result = {
          success: false,
          message: "MongoDB connection failed - invalid state",
          details: status,
          timestamp: new Date()
        };
        this.addResult(false, "Connection test failed", status);
        console.log("❌ Connection test: FAILED");
        return result;
      }
    } catch (error) {
      const result = {
        success: false,
        message: `MongoDB connection error: ${error}`,
        details: { error: error instanceof Error ? error.message : String(error) },
        timestamp: new Date()
      };
      this.addResult(false, "Connection test failed with error", result.details);
      console.log("❌ Connection test: FAILED with error");
      return result;
    }
  }

  async testDatabaseOperations(): Promise<DatabaseTestResult[]> {
    console.log("\n🔍 Testing Database Operations...");
    const operationResults: DatabaseTestResult[] = [];

    try {
      // Test 1: Create a test document
      console.log("📝 Testing document creation...");
      const testTool = {
        name: `Test Tool ${Date.now()}`,
        description: `Test description created at ${new Date().toISOString()}`,
        status: 'available'
      };

      const createdTool = await Tool.create(testTool);
      operationResults.push({
        success: true,
        message: "Document creation successful",
        details: { id: createdTool._id, name: createdTool.name },
        timestamp: new Date()
      });
      console.log("✅ Create operation: PASSED");

      // Test 2: Read the document
      console.log("📖 Testing document retrieval...");
      const foundTool = await Tool.findById(createdTool._id);
      if (foundTool) {
        operationResults.push({
          success: true,
          message: "Document retrieval successful",
          details: { id: foundTool._id, name: foundTool.name },
          timestamp: new Date()
        });
        console.log("✅ Read operation: PASSED");
      } else {
        operationResults.push({
          success: false,
          message: "Document retrieval failed - not found",
          details: { searchId: createdTool._id },
          timestamp: new Date()
        });
        console.log("❌ Read operation: FAILED");
      }

      // Test 3: Update the document
      console.log("✏️ Testing document update...");
      const updatedTool = await Tool.findByIdAndUpdate(
        createdTool._id,
        { description: `Updated at ${new Date().toISOString()}` },
        { new: true }
      );
      if (updatedTool) {
        operationResults.push({
          success: true,
          message: "Document update successful",
          details: { id: updatedTool._id, description: updatedTool.description },
          timestamp: new Date()
        });
        console.log("✅ Update operation: PASSED");
      } else {
        operationResults.push({
          success: false,
          message: "Document update failed",
          details: { searchId: createdTool._id },
          timestamp: new Date()
        });
        console.log("❌ Update operation: FAILED");
      }

      // Test 4: Query multiple documents
      console.log("🔍 Testing document query...");
      const allTools = await Tool.find().limit(5);
      operationResults.push({
        success: true,
        message: "Document query successful",
        details: { count: allTools.length, tools: allTools.map(t => ({ id: t._id, name: t.name })) },
        timestamp: new Date()
      });
      console.log("✅ Query operation: PASSED");

      // Test 5: Delete the test document
      console.log("🗑️ Testing document deletion...");
      const deletedTool = await Tool.findByIdAndDelete(createdTool._id);
      if (deletedTool) {
        operationResults.push({
          success: true,
          message: "Document deletion successful",
          details: { id: deletedTool._id },
          timestamp: new Date()
        });
        console.log("✅ Delete operation: PASSED");
      } else {
        operationResults.push({
          success: false,
          message: "Document deletion failed",
          details: { searchId: createdTool._id },
          timestamp: new Date()
        });
        console.log("❌ Delete operation: FAILED");
      }

    } catch (error) {
      operationResults.push({
        success: false,
        message: `Database operation error: ${error}`,
        details: { error: error instanceof Error ? error.message : String(error) },
        timestamp: new Date()
      });
      console.log("❌ Database operations: FAILED with error");
    }

    this.results.push(...operationResults);
    return operationResults;
  }

  async testCollectionStats(): Promise<DatabaseTestResult> {
    console.log("\n📊 Testing Collection Statistics...");
    
    try {
      const db = mongoose.connection.db;
      if (!db) {
        throw new Error("Database connection not available");
      }

      // Get collection stats
      const collections = await db.listCollections().toArray();
      const toolsCollection = db.collection('tools');
      const toolCount = await toolsCollection.countDocuments();
      
      const stats = {
        collections: collections.map(c => c.name),
        toolCount,
        databaseName: db.databaseName
      };

      const result = {
        success: true,
        message: "Collection statistics retrieved successfully",
        details: stats,
        timestamp: new Date()
      };

      this.addResult(true, "Collection stats test passed", stats);
      console.log("✅ Collection stats: PASSED");
      console.log(`📈 Tools in database: ${toolCount}`);
      console.log(`📂 Collections: ${collections.length}`);
      
      return result;
    } catch (error) {
      const result = {
        success: false,
        message: `Collection statistics error: ${error}`,
        details: { error: error instanceof Error ? error.message : String(error) },
        timestamp: new Date()
      };
      this.addResult(false, "Collection stats test failed", result.details);
      console.log("❌ Collection stats: FAILED");
      return result;
    }
  }

  async runAllTests(): Promise<DatabaseTestResult[]> {
    console.log("🚀 Starting comprehensive database tests...");
    console.log("=" * 50);

    const allResults: DatabaseTestResult[] = [];

    // Test 1: Connection
    const connectionResult = await this.testConnection();
    allResults.push(connectionResult);

    if (connectionResult.success) {
      // Test 2: Database operations
      const operationResults = await this.testDatabaseOperations();
      allResults.push(...operationResults);

      // Test 3: Collection statistics
      const statsResult = await this.testCollectionStats();
      allResults.push(statsResult);
    } else {
      console.log("⚠️ Skipping further tests due to connection failure");
    }

    return allResults;
  }

  getResults(): DatabaseTestResult[] {
    return this.results;
  }

  printSummary(): void {
    console.log("\n" + "=" * 50);
    console.log("📋 TEST SUMMARY");
    console.log("=" * 50);
    
    const passed = this.results.filter(r => r.success).length;
    const failed = this.results.filter(r => !r.success).length;
    
    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`📊 Total: ${this.results.length}`);
    
    if (failed > 0) {
      console.log("\n❌ Failed Tests:");
      this.results.filter(r => !r.success).forEach((result, index) => {
        console.log(`${index + 1}. ${result.message}`);
        if (result.details) {
          console.log(`   Details: ${JSON.stringify(result.details, null, 2)}`);
        }
      });
    }
    
    console.log("=" * 50);
  }
}

export default DatabaseTester;
