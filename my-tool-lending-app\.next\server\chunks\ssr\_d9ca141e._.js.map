{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Community%20Tool%20Lending%20Platform/my-tool-lending-app/src/app/add-tool/page.tsx"], "sourcesContent": ["'use client'; // This marks the component as a Client Component\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/navigation';\n\nexport default function AddToolPage() {\n  const [name, setName] = useState('');\n  const [description, setDescription] = useState('');\n  const router = useRouter();\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    if (!name || !description) {\n      alert('Name and description are required.');\n      return;\n    }\n\n    try {\n      const res = await fetch('/api/tools', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ name, description }),\n      });\n\n      if (res.ok) {\n        router.push('/'); // Redirect to homepage on success\n        router.refresh(); // Tell Next.js to refresh the data on the homepage\n      } else {\n        throw new Error('Failed to create a tool');\n      }\n    } catch (error) {\n      console.log(error);\n    }\n  };\n\n  return (\n    <div className=\"max-w-md mx-auto mt-10\">\n      <h1 className=\"text-2xl font-bold mb-4\">Add a New Tool</h1>\n      <form onSubmit={handleSubmit} className=\"bg-white p-6 rounded-lg shadow-md\">\n        <div className=\"mb-4\">\n          <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700\">Tool Name</label>\n          <input\n            id=\"name\"\n            type=\"text\"\n            onChange={(e) => setName(e.target.value)}\n            value={name}\n            className=\"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\"\n            placeholder=\"e.g., Electric Drill\"\n          />\n        </div>\n        <div className=\"mb-4\">\n          <label htmlFor=\"description\" className=\"block text-sm font-medium text-gray-700\">Description</label>\n          <textarea\n            id=\"description\"\n            onChange={(e) => setDescription(e.target.value)}\n            value={description}\n            className=\"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500\"\n            placeholder=\"e.g., Cordless, 18V, comes with two batteries.\"\n          />\n        </div>\n        <button\n          type=\"submit\"\n          className=\"w-full py-2 px-4 bg-blue-600 text-white font-semibold rounded-md hover:bg-blue-700\"\n        >\n          Add Tool\n        </button>\n      </form>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA,cAAc,iDAAiD;;;;AAKhD,SAAS;IACtB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,QAAQ,CAAC,aAAa;YACzB,MAAM;YACN;QACF;QAEA,IAAI;YACF,MAAM,MAAM,MAAM,MAAM,cAAc;gBACpC,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAM;gBAAY;YAC3C;YAEA,IAAI,IAAI,EAAE,EAAE;gBACV,OAAO,IAAI,CAAC,MAAM,kCAAkC;gBACpD,OAAO,OAAO,IAAI,mDAAmD;YACvE,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC;QACd;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAA0B;;;;;;0BACxC,8OAAC;gBAAK,UAAU;gBAAc,WAAU;;kCACtC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,SAAQ;gCAAO,WAAU;0CAA0C;;;;;;0CAC1E,8OAAC;gCACC,IAAG;gCACH,MAAK;gCACL,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;gCACvC,OAAO;gCACP,WAAU;gCACV,aAAY;;;;;;;;;;;;kCAGhB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,SAAQ;gCAAc,WAAU;0CAA0C;;;;;;0CACjF,8OAAC;gCACC,IAAG;gCACH,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gCAC9C,OAAO;gCACP,WAAU;gCACV,aAAY;;;;;;;;;;;;kCAGhB,8OAAC;wBACC,MAAK;wBACL,WAAU;kCACX;;;;;;;;;;;;;;;;;;AAMT", "debugId": null}}, {"offset": {"line": 149, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Community%20Tool%20Lending%20Platform/my-tool-lending-app/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 156, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Community%20Tool%20Lending%20Platform/my-tool-lending-app/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}