{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Community%20Tool%20Lending%20Platform/my-tool-lending-app/src/app/add-tool/page.tsx"], "sourcesContent": ["'use client'; // This marks the component as a Client Component\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/navigation';\n\nexport default function AddToolPage() {\n  const [name, setName] = useState('');\n  const [description, setDescription] = useState('');\n  const router = useRouter();\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    if (!name || !description) {\n      alert('Name and description are required.');\n      return;\n    }\n\n    try {\n      const res = await fetch('/api/tools', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ name, description }),\n      });\n\n      if (res.ok) {\n        router.push('/'); // Redirect to homepage on success\n        router.refresh(); // Tell Next.js to refresh the data on the homepage\n      } else {\n        throw new Error('Failed to create a tool');\n      }\n    } catch (error) {\n      console.log(error);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-12\">\n      <div className=\"max-w-lg mx-auto px-4\">\n        {/* Header */}\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-4xl font-bold text-gray-800 mb-4\">Add a New Tool</h1>\n          <p className=\"text-gray-600\">Share your tools with the community and help your neighbors</p>\n        </div>\n\n        {/* Form */}\n        <div className=\"bg-white rounded-2xl shadow-xl p-8\">\n          <form onSubmit={handleSubmit} className=\"space-y-6\">\n            <div>\n              <label htmlFor=\"name\" className=\"block text-sm font-semibold text-gray-700 mb-2\">\n                Tool Name *\n              </label>\n              <input\n                id=\"name\"\n                type=\"text\"\n                onChange={(e) => setName(e.target.value)}\n                value={name}\n                className=\"w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200\"\n                placeholder=\"e.g., Electric Drill, Lawn Mower, Hammer\"\n                required\n              />\n            </div>\n\n            <div>\n              <label htmlFor=\"description\" className=\"block text-sm font-semibold text-gray-700 mb-2\">\n                Description *\n              </label>\n              <textarea\n                id=\"description\"\n                onChange={(e) => setDescription(e.target.value)}\n                value={description}\n                rows={4}\n                className=\"w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 resize-none\"\n                placeholder=\"Describe your tool, its condition, and any special features or accessories included...\"\n                required\n              />\n            </div>\n\n            <div className=\"pt-4\">\n              <button\n                type=\"submit\"\n                className=\"w-full py-4 px-6 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-bold rounded-lg hover:from-blue-600 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 transform hover:scale-105 shadow-lg\"\n              >\n                Add Tool to Community\n              </button>\n            </div>\n          </form>\n\n          {/* Help Text */}\n          <div className=\"mt-6 p-4 bg-blue-50 rounded-lg\">\n            <p className=\"text-sm text-blue-800\">\n              <strong>Tip:</strong> Be descriptive! Include details like brand, model, condition, and any accessories to help community members understand what you're sharing.\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA,cAAc,iDAAiD;;;;AAKhD,SAAS;IACtB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,QAAQ,CAAC,aAAa;YACzB,MAAM;YACN;QACF;QAEA,IAAI;YACF,MAAM,MAAM,MAAM,MAAM,cAAc;gBACpC,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAM;gBAAY;YAC3C;YAEA,IAAI,IAAI,EAAE,EAAE;gBACV,OAAO,IAAI,CAAC,MAAM,kCAAkC;gBACpD,OAAO,OAAO,IAAI,mDAAmD;YACvE,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC;QACd;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;8BAI/B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAK,UAAU;4BAAc,WAAU;;8CACtC,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAO,WAAU;sDAAiD;;;;;;sDAGjF,8OAAC;4CACC,IAAG;4CACH,MAAK;4CACL,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;4CACvC,OAAO;4CACP,WAAU;4CACV,aAAY;4CACZ,QAAQ;;;;;;;;;;;;8CAIZ,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAc,WAAU;sDAAiD;;;;;;sDAGxF,8OAAC;4CACC,IAAG;4CACH,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4CAC9C,OAAO;4CACP,MAAM;4CACN,WAAU;4CACV,aAAY;4CACZ,QAAQ;;;;;;;;;;;;8CAIZ,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;sCAOL,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;;kDACX,8OAAC;kDAAO;;;;;;oCAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnC", "debugId": null}}, {"offset": {"line": 214, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Community%20Tool%20Lending%20Platform/my-tool-lending-app/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 221, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Community%20Tool%20Lending%20Platform/my-tool-lending-app/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}