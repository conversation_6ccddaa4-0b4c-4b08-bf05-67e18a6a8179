{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Community%20Tool%20Lending%20Platform/my-tool-lending-app/src/app/add-tool/page.tsx"], "sourcesContent": ["'use client'; // This marks the component as a Client Component\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/navigation';\n\nexport default function AddToolPage() {\n  const [name, setName] = useState('');\n  const [description, setDescription] = useState('');\n  const [category, setCategory] = useState('Other');\n  const [condition, setCondition] = useState('Good');\n  const [city, setCity] = useState('');\n  const [area, setArea] = useState('');\n  const [postalCode, setPostalCode] = useState('');\n  const [ownerName, setOwnerName] = useState('');\n  const [ownerEmail, setOwnerEmail] = useState('');\n  const [ownerPhone, setOwnerPhone] = useState('');\n  const [maxDuration, setMaxDuration] = useState(7);\n  const [deposit, setDeposit] = useState(0);\n  const [instructions, setInstructions] = useState('');\n  const [tags, setTags] = useState('');\n  const router = useRouter();\n\n  const categories = [\n    'Power Tools', 'Hand Tools', 'Garden Tools', 'Automotive',\n    'Construction', 'Electrical', 'Plumbing', 'Cleaning',\n    'Kitchen Appliances', 'Sports & Recreation', 'Other'\n  ];\n\n  const conditions = ['Excellent', 'Good', 'Fair', 'Poor'];\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    // Validation\n    if (!name || !description || !city || !area || !ownerName || !ownerEmail) {\n      alert('Please fill in all required fields.');\n      return;\n    }\n\n    const toolData = {\n      name,\n      description,\n      category,\n      condition,\n      location: {\n        city,\n        area,\n        postalCode\n      },\n      owner: {\n        name: ownerName,\n        email: ownerEmail,\n        phone: ownerPhone\n      },\n      borrowingTerms: {\n        maxDuration: Number(maxDuration),\n        deposit: Number(deposit),\n        instructions\n      },\n      tags: tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0)\n    };\n\n    try {\n      const res = await fetch('/api/tools', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(toolData),\n      });\n\n      if (res.ok) {\n        alert('Tool added successfully!');\n        router.push('/'); // Redirect to homepage on success\n        router.refresh(); // Tell Next.js to refresh the data on the homepage\n      } else {\n        const errorData = await res.json();\n        throw new Error(errorData.error || 'Failed to create a tool');\n      }\n    } catch (error) {\n      console.log(error);\n      alert('Error creating tool: ' + error);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-12\">\n      <div className=\"max-w-lg mx-auto px-4\">\n        {/* Header */}\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-4xl font-bold text-gray-800 mb-4\">Add a New Tool</h1>\n          <p className=\"text-gray-600\">Share your tools with the community and help your neighbors</p>\n        </div>\n\n        {/* Form */}\n        <div className=\"bg-white rounded-2xl shadow-xl p-8\">\n          <form onSubmit={handleSubmit} className=\"space-y-6\">\n            {/* Basic Information */}\n            <div className=\"border-b border-gray-200 pb-6\">\n              <h3 className=\"text-lg font-semibold text-gray-800 mb-4\">Basic Information</h3>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <label htmlFor=\"name\" className=\"block text-sm font-semibold text-gray-700 mb-2\">\n                    Tool Name *\n                  </label>\n                  <input\n                    id=\"name\"\n                    type=\"text\"\n                    onChange={(e) => setName(e.target.value)}\n                    value={name}\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 text-gray-900 bg-white\"\n                    placeholder=\"e.g., Electric Drill, Lawn Mower\"\n                    required\n                  />\n                </div>\n\n                <div>\n                  <label htmlFor=\"category\" className=\"block text-sm font-semibold text-gray-700 mb-2\">\n                    Category *\n                  </label>\n                  <select\n                    id=\"category\"\n                    onChange={(e) => setCategory(e.target.value)}\n                    value={category}\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 text-gray-900 bg-white\"\n                    required\n                  >\n                    {categories.map(cat => (\n                      <option key={cat} value={cat}>{cat}</option>\n                    ))}\n                  </select>\n                </div>\n              </div>\n\n              <div className=\"mt-4\">\n                <label htmlFor=\"description\" className=\"block text-sm font-semibold text-gray-700 mb-2\">\n                  Description *\n                </label>\n                <textarea\n                  id=\"description\"\n                  onChange={(e) => setDescription(e.target.value)}\n                  value={description}\n                  rows={3}\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 resize-none text-gray-900 bg-white\"\n                  placeholder=\"Describe your tool, its condition, and any special features...\"\n                  required\n                />\n              </div>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mt-4\">\n                <div>\n                  <label htmlFor=\"condition\" className=\"block text-sm font-semibold text-gray-700 mb-2\">\n                    Condition\n                  </label>\n                  <select\n                    id=\"condition\"\n                    onChange={(e) => setCondition(e.target.value)}\n                    value={condition}\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 text-gray-900 bg-white\"\n                  >\n                    {conditions.map(cond => (\n                      <option key={cond} value={cond}>{cond}</option>\n                    ))}\n                  </select>\n                </div>\n\n                <div>\n                  <label htmlFor=\"tags\" className=\"block text-sm font-semibold text-gray-700 mb-2\">\n                    Tags (comma separated)\n                  </label>\n                  <input\n                    id=\"tags\"\n                    type=\"text\"\n                    onChange={(e) => setTags(e.target.value)}\n                    value={tags}\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 text-gray-900 bg-white\"\n                    placeholder=\"e.g., cordless, 18V, battery\"\n                  />\n                </div>\n              </div>\n            </div>\n\n            {/* Location Information */}\n            <div className=\"border-b border-gray-200 pb-6\">\n              <h3 className=\"text-lg font-semibold text-gray-800 mb-4\">Location</h3>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                <div>\n                  <label htmlFor=\"city\" className=\"block text-sm font-semibold text-gray-700 mb-2\">\n                    City *\n                  </label>\n                  <input\n                    id=\"city\"\n                    type=\"text\"\n                    onChange={(e) => setCity(e.target.value)}\n                    value={city}\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 text-gray-900 bg-white\"\n                    placeholder=\"e.g., Colombo\"\n                    required\n                  />\n                </div>\n\n                <div>\n                  <label htmlFor=\"area\" className=\"block text-sm font-semibold text-gray-700 mb-2\">\n                    Area/District *\n                  </label>\n                  <input\n                    id=\"area\"\n                    type=\"text\"\n                    onChange={(e) => setArea(e.target.value)}\n                    value={area}\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 text-gray-900 bg-white\"\n                    placeholder=\"e.g., Nugegoda\"\n                    required\n                  />\n                </div>\n\n                <div>\n                  <label htmlFor=\"postalCode\" className=\"block text-sm font-semibold text-gray-700 mb-2\">\n                    Postal Code\n                  </label>\n                  <input\n                    id=\"postalCode\"\n                    type=\"text\"\n                    onChange={(e) => setPostalCode(e.target.value)}\n                    value={postalCode}\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 text-gray-900 bg-white\"\n                    placeholder=\"e.g., 10250\"\n                  />\n                </div>\n              </div>\n            </div>\n\n            {/* Owner Information */}\n            <div className=\"border-b border-gray-200 pb-6\">\n              <h3 className=\"text-lg font-semibold text-gray-800 mb-4\">Contact Information</h3>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <label htmlFor=\"ownerName\" className=\"block text-sm font-semibold text-gray-700 mb-2\">\n                    Your Name *\n                  </label>\n                  <input\n                    id=\"ownerName\"\n                    type=\"text\"\n                    onChange={(e) => setOwnerName(e.target.value)}\n                    value={ownerName}\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 text-gray-900 bg-white\"\n                    placeholder=\"Your full name\"\n                    required\n                  />\n                </div>\n\n                <div>\n                  <label htmlFor=\"ownerEmail\" className=\"block text-sm font-semibold text-gray-700 mb-2\">\n                    Email Address *\n                  </label>\n                  <input\n                    id=\"ownerEmail\"\n                    type=\"email\"\n                    onChange={(e) => setOwnerEmail(e.target.value)}\n                    value={ownerEmail}\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 text-gray-900 bg-white\"\n                    placeholder=\"<EMAIL>\"\n                    required\n                  />\n                </div>\n              </div>\n\n              <div className=\"mt-4\">\n                <label htmlFor=\"ownerPhone\" className=\"block text-sm font-semibold text-gray-700 mb-2\">\n                  Phone Number (Optional)\n                </label>\n                <input\n                  id=\"ownerPhone\"\n                  type=\"tel\"\n                  onChange={(e) => setOwnerPhone(e.target.value)}\n                  value={ownerPhone}\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 text-gray-900 bg-white\"\n                  placeholder=\"+94 77 123 4567\"\n                />\n              </div>\n            </div>\n\n            {/* Borrowing Terms */}\n            <div className=\"pb-6\">\n              <h3 className=\"text-lg font-semibold text-gray-800 mb-4\">Borrowing Terms</h3>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <label htmlFor=\"maxDuration\" className=\"block text-sm font-semibold text-gray-700 mb-2\">\n                    Maximum Loan Duration (days)\n                  </label>\n                  <input\n                    id=\"maxDuration\"\n                    type=\"number\"\n                    min=\"1\"\n                    max=\"30\"\n                    onChange={(e) => setMaxDuration(Number(e.target.value))}\n                    value={maxDuration}\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 text-gray-900 bg-white\"\n                  />\n                </div>\n\n                <div>\n                  <label htmlFor=\"deposit\" className=\"block text-sm font-semibold text-gray-700 mb-2\">\n                    Security Deposit (LKR)\n                  </label>\n                  <input\n                    id=\"deposit\"\n                    type=\"number\"\n                    min=\"0\"\n                    onChange={(e) => setDeposit(Number(e.target.value))}\n                    value={deposit}\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 text-gray-900 bg-white\"\n                    placeholder=\"0\"\n                  />\n                </div>\n              </div>\n\n              <div className=\"mt-4\">\n                <label htmlFor=\"instructions\" className=\"block text-sm font-semibold text-gray-700 mb-2\">\n                  Special Instructions\n                </label>\n                <textarea\n                  id=\"instructions\"\n                  onChange={(e) => setInstructions(e.target.value)}\n                  value={instructions}\n                  rows={3}\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 resize-none text-gray-900 bg-white\"\n                  placeholder=\"Any special care instructions, pickup arrangements, etc.\"\n                />\n              </div>\n            </div>\n\n            <div className=\"pt-4\">\n              <button\n                type=\"submit\"\n                className=\"w-full py-4 px-6 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-bold rounded-lg hover:from-blue-600 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 transform hover:scale-105 shadow-lg\"\n              >\n                Add Tool to Community\n              </button>\n            </div>\n          </form>\n\n          {/* Help Text */}\n          <div className=\"mt-6 p-4 bg-blue-50 rounded-lg\">\n            <p className=\"text-sm text-blue-800\">\n              <strong>Tip:</strong> Be descriptive! Include details like brand, model, condition, and any accessories to help community members understand what you're sharing.\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA,cAAc,iDAAiD;;;;AAKhD,SAAS;IACtB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,aAAa;QACjB;QAAe;QAAc;QAAgB;QAC7C;QAAgB;QAAc;QAAY;QAC1C;QAAsB;QAAuB;KAC9C;IAED,MAAM,aAAa;QAAC;QAAa;QAAQ;QAAQ;KAAO;IAExD,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,aAAa;QACb,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,aAAa,CAAC,YAAY;YACxE,MAAM;YACN;QACF;QAEA,MAAM,WAAW;YACf;YACA;YACA;YACA;YACA,UAAU;gBACR;gBACA;gBACA;YACF;YACA,OAAO;gBACL,MAAM;gBACN,OAAO;gBACP,OAAO;YACT;YACA,gBAAgB;gBACd,aAAa,OAAO;gBACpB,SAAS,OAAO;gBAChB;YACF;YACA,MAAM,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,MAAO,IAAI,IAAI,IAAI,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,GAAG;QAC1E;QAEA,IAAI;YACF,MAAM,MAAM,MAAM,MAAM,cAAc;gBACpC,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,IAAI,EAAE,EAAE;gBACV,MAAM;gBACN,OAAO,IAAI,CAAC,MAAM,kCAAkC;gBACpD,OAAO,OAAO,IAAI,mDAAmD;YACvE,OAAO;gBACL,MAAM,YAAY,MAAM,IAAI,IAAI;gBAChC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI;YACrC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC;YACZ,MAAM,0BAA0B;QAClC;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;8BAI/B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAK,UAAU;4BAAc,WAAU;;8CAEtC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDAEzD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAM,SAAQ;4DAAO,WAAU;sEAAiD;;;;;;sEAGjF,8OAAC;4DACC,IAAG;4DACH,MAAK;4DACL,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;4DACvC,OAAO;4DACP,WAAU;4DACV,aAAY;4DACZ,QAAQ;;;;;;;;;;;;8DAIZ,8OAAC;;sEACC,8OAAC;4DAAM,SAAQ;4DAAW,WAAU;sEAAiD;;;;;;sEAGrF,8OAAC;4DACC,IAAG;4DACH,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;4DAC3C,OAAO;4DACP,WAAU;4DACV,QAAQ;sEAEP,WAAW,GAAG,CAAC,CAAA,oBACd,8OAAC;oEAAiB,OAAO;8EAAM;mEAAlB;;;;;;;;;;;;;;;;;;;;;;sDAMrB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,SAAQ;oDAAc,WAAU;8DAAiD;;;;;;8DAGxF,8OAAC;oDACC,IAAG;oDACH,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oDAC9C,OAAO;oDACP,MAAM;oDACN,WAAU;oDACV,aAAY;oDACZ,QAAQ;;;;;;;;;;;;sDAIZ,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAM,SAAQ;4DAAY,WAAU;sEAAiD;;;;;;sEAGtF,8OAAC;4DACC,IAAG;4DACH,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;4DAC5C,OAAO;4DACP,WAAU;sEAET,WAAW,GAAG,CAAC,CAAA,qBACd,8OAAC;oEAAkB,OAAO;8EAAO;mEAApB;;;;;;;;;;;;;;;;8DAKnB,8OAAC;;sEACC,8OAAC;4DAAM,SAAQ;4DAAO,WAAU;sEAAiD;;;;;;sEAGjF,8OAAC;4DACC,IAAG;4DACH,MAAK;4DACL,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;4DACvC,OAAO;4DACP,WAAU;4DACV,aAAY;;;;;;;;;;;;;;;;;;;;;;;;8CAOpB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDAEzD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAM,SAAQ;4DAAO,WAAU;sEAAiD;;;;;;sEAGjF,8OAAC;4DACC,IAAG;4DACH,MAAK;4DACL,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;4DACvC,OAAO;4DACP,WAAU;4DACV,aAAY;4DACZ,QAAQ;;;;;;;;;;;;8DAIZ,8OAAC;;sEACC,8OAAC;4DAAM,SAAQ;4DAAO,WAAU;sEAAiD;;;;;;sEAGjF,8OAAC;4DACC,IAAG;4DACH,MAAK;4DACL,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;4DACvC,OAAO;4DACP,WAAU;4DACV,aAAY;4DACZ,QAAQ;;;;;;;;;;;;8DAIZ,8OAAC;;sEACC,8OAAC;4DAAM,SAAQ;4DAAa,WAAU;sEAAiD;;;;;;sEAGvF,8OAAC;4DACC,IAAG;4DACH,MAAK;4DACL,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4DAC7C,OAAO;4DACP,WAAU;4DACV,aAAY;;;;;;;;;;;;;;;;;;;;;;;;8CAOpB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDAEzD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAM,SAAQ;4DAAY,WAAU;sEAAiD;;;;;;sEAGtF,8OAAC;4DACC,IAAG;4DACH,MAAK;4DACL,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;4DAC5C,OAAO;4DACP,WAAU;4DACV,aAAY;4DACZ,QAAQ;;;;;;;;;;;;8DAIZ,8OAAC;;sEACC,8OAAC;4DAAM,SAAQ;4DAAa,WAAU;sEAAiD;;;;;;sEAGvF,8OAAC;4DACC,IAAG;4DACH,MAAK;4DACL,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4DAC7C,OAAO;4DACP,WAAU;4DACV,aAAY;4DACZ,QAAQ;;;;;;;;;;;;;;;;;;sDAKd,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,SAAQ;oDAAa,WAAU;8DAAiD;;;;;;8DAGvF,8OAAC;oDACC,IAAG;oDACH,MAAK;oDACL,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oDAC7C,OAAO;oDACP,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;8CAMlB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDAEzD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAM,SAAQ;4DAAc,WAAU;sEAAiD;;;;;;sEAGxF,8OAAC;4DACC,IAAG;4DACH,MAAK;4DACL,KAAI;4DACJ,KAAI;4DACJ,UAAU,CAAC,IAAM,eAAe,OAAO,EAAE,MAAM,CAAC,KAAK;4DACrD,OAAO;4DACP,WAAU;;;;;;;;;;;;8DAId,8OAAC;;sEACC,8OAAC;4DAAM,SAAQ;4DAAU,WAAU;sEAAiD;;;;;;sEAGpF,8OAAC;4DACC,IAAG;4DACH,MAAK;4DACL,KAAI;4DACJ,UAAU,CAAC,IAAM,WAAW,OAAO,EAAE,MAAM,CAAC,KAAK;4DACjD,OAAO;4DACP,WAAU;4DACV,aAAY;;;;;;;;;;;;;;;;;;sDAKlB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,SAAQ;oDAAe,WAAU;8DAAiD;;;;;;8DAGzF,8OAAC;oDACC,IAAG;oDACH,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;oDAC/C,OAAO;oDACP,MAAM;oDACN,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;8CAKlB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;sCAOL,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;;kDACX,8OAAC;kDAAO;;;;;;oCAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnC", "debugId": null}}, {"offset": {"line": 751, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Community%20Tool%20Lending%20Platform/my-tool-lending-app/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 758, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Community%20Tool%20Lending%20Platform/my-tool-lending-app/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}