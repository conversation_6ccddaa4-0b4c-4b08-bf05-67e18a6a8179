{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Community%20Tool%20Lending%20Platform/my-tool-lending-app/src/components/RemoveBtn.tsx"], "sourcesContent": ["'use client';\n\nimport { HiOutlineTrash } from 'react-icons/hi';\nimport { useRouter } from 'next/navigation';\n\nexport default function RemoveBtn({ id }: { id: string }) {\n    const router = useRouter();\n    const removeTool = async () => {\n        const confirmed = confirm('Are you sure you want to delete this tool?');\n\n        if (confirmed) {\n            const res = await fetch(`/api/tools?id=${id}`, {\n                method: 'DELETE',\n            });\n            \n            if (res.ok) {\n                router.refresh();\n            }\n        }\n    };\n\n    return (\n        <button\n            onClick={removeTool}\n            className=\"text-red-600 hover:text-red-700 transition-colors duration-200\"\n            title=\"Delete Tool\"\n        >\n            <HiOutlineTrash size={18} />\n        </button>\n    );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS,UAAU,EAAE,EAAE,EAAkB;IACpD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,aAAa;QACf,MAAM,YAAY,QAAQ;QAE1B,IAAI,WAAW;YACX,MAAM,MAAM,MAAM,MAAM,CAAC,cAAc,EAAE,IAAI,EAAE;gBAC3C,QAAQ;YACZ;YAEA,IAAI,IAAI,EAAE,EAAE;gBACR,OAAO,OAAO;YAClB;QACJ;IACJ;IAEA,qBACI,8OAAC;QACG,SAAS;QACT,WAAU;QACV,OAAM;kBAEN,cAAA,8OAAC,8IAAA,CAAA,iBAAc;YAAC,MAAM;;;;;;;;;;;AAGlC", "debugId": null}}, {"offset": {"line": 53, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Community%20Tool%20Lending%20Platform/my-tool-lending-app/src/components/SearchAndFilter.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { HiSearch, HiFilter, HiX } from 'react-icons/hi';\n\ninterface SearchAndFilterProps {\n  onSearch: (searchParams: SearchParams) => void;\n  filterOptions?: FilterOptions;\n  isLoading?: boolean;\n}\n\ninterface SearchParams {\n  query: string;\n  category: string;\n  city: string;\n  availability: string;\n  condition: string;\n  sortBy: string;\n  sortOrder: string;\n}\n\ninterface FilterOptions {\n  categories: string[];\n  cities: string[];\n  conditions: string[];\n  availabilities: string[];\n}\n\nexport default function SearchAndFilter({ onSearch, filterOptions, isLoading }: SearchAndFilterProps) {\n  const [searchParams, setSearchParams] = useState<SearchParams>({\n    query: '',\n    category: 'all',\n    city: '',\n    availability: 'all',\n    condition: 'all',\n    sortBy: 'createdAt',\n    sortOrder: 'desc'\n  });\n\n  const [showFilters, setShowFilters] = useState(false);\n\n  // Trigger search when parameters change\n  useEffect(() => {\n    const timeoutId = setTimeout(() => {\n      onSearch(searchParams);\n    }, 300); // Debounce search\n\n    return () => clearTimeout(timeoutId);\n  }, [searchParams, onSearch]);\n\n  const updateSearchParam = (key: keyof SearchParams, value: string) => {\n    setSearchParams(prev => ({\n      ...prev,\n      [key]: value\n    }));\n  };\n\n  const clearFilters = () => {\n    setSearchParams({\n      query: '',\n      category: 'all',\n      city: '',\n      availability: 'all',\n      condition: 'all',\n      sortBy: 'createdAt',\n      sortOrder: 'desc'\n    });\n  };\n\n  const hasActiveFilters = searchParams.query || \n    searchParams.category !== 'all' || \n    searchParams.city || \n    searchParams.availability !== 'all' || \n    searchParams.condition !== 'all';\n\n  return (\n    <div className=\"bg-white rounded-xl shadow-lg p-6 mb-8\">\n      {/* Search Bar */}\n      <div className=\"relative mb-4\">\n        <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n          <HiSearch className=\"h-5 w-5 text-gray-400\" />\n        </div>\n        <input\n          type=\"text\"\n          placeholder=\"Search tools by name, description, or tags...\"\n          value={searchParams.query}\n          onChange={(e) => updateSearchParam('query', e.target.value)}\n          className=\"block w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n        />\n        {isLoading && (\n          <div className=\"absolute inset-y-0 right-0 pr-3 flex items-center\">\n            <div className=\"animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500\"></div>\n          </div>\n        )}\n      </div>\n\n      {/* Filter Toggle */}\n      <div className=\"flex justify-between items-center mb-4\">\n        <button\n          onClick={() => setShowFilters(!showFilters)}\n          className=\"flex items-center space-x-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors duration-200\"\n        >\n          <HiFilter className=\"h-4 w-4\" />\n          <span>Filters</span>\n          {hasActiveFilters && (\n            <span className=\"bg-blue-500 text-white text-xs rounded-full px-2 py-1\">\n              Active\n            </span>\n          )}\n        </button>\n\n        {hasActiveFilters && (\n          <button\n            onClick={clearFilters}\n            className=\"flex items-center space-x-1 px-3 py-2 text-red-600 hover:text-red-700 transition-colors duration-200\"\n          >\n            <HiX className=\"h-4 w-4\" />\n            <span>Clear Filters</span>\n          </button>\n        )}\n      </div>\n\n      {/* Filters */}\n      {showFilters && (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4 p-4 bg-gray-50 rounded-lg\">\n          {/* Category Filter */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">Category</label>\n            <select\n              value={searchParams.category}\n              onChange={(e) => updateSearchParam('category', e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n            >\n              <option value=\"all\">All Categories</option>\n              {filterOptions?.categories?.map(category => (\n                <option key={category} value={category}>{category}</option>\n              ))}\n            </select>\n          </div>\n\n          {/* City Filter */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">City</label>\n            <input\n              type=\"text\"\n              placeholder=\"Enter city\"\n              value={searchParams.city}\n              onChange={(e) => updateSearchParam('city', e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n            />\n          </div>\n\n          {/* Availability Filter */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">Availability</label>\n            <select\n              value={searchParams.availability}\n              onChange={(e) => updateSearchParam('availability', e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n            >\n              <option value=\"all\">All Status</option>\n              <option value=\"available\">Available</option>\n              <option value=\"borrowed\">Borrowed</option>\n              <option value=\"maintenance\">Maintenance</option>\n              <option value=\"unavailable\">Unavailable</option>\n            </select>\n          </div>\n\n          {/* Condition Filter */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">Condition</label>\n            <select\n              value={searchParams.condition}\n              onChange={(e) => updateSearchParam('condition', e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n            >\n              <option value=\"all\">All Conditions</option>\n              <option value=\"Excellent\">Excellent</option>\n              <option value=\"Good\">Good</option>\n              <option value=\"Fair\">Fair</option>\n              <option value=\"Poor\">Poor</option>\n            </select>\n          </div>\n\n          {/* Sort Options */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">Sort By</label>\n            <div className=\"space-y-2\">\n              <select\n                value={searchParams.sortBy}\n                onChange={(e) => updateSearchParam('sortBy', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              >\n                <option value=\"createdAt\">Date Added</option>\n                <option value=\"name\">Name</option>\n                <option value=\"category\">Category</option>\n                <option value=\"location.city\">City</option>\n              </select>\n              <select\n                value={searchParams.sortOrder}\n                onChange={(e) => updateSearchParam('sortOrder', e.target.value)}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n              >\n                <option value=\"desc\">Newest First</option>\n                <option value=\"asc\">Oldest First</option>\n              </select>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AA4Be,SAAS,gBAAgB,EAAE,QAAQ,EAAE,aAAa,EAAE,SAAS,EAAwB;IAClG,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;QAC7D,OAAO;QACP,UAAU;QACV,MAAM;QACN,cAAc;QACd,WAAW;QACX,QAAQ;QACR,WAAW;IACb;IAEA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,wCAAwC;IACxC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY,WAAW;YAC3B,SAAS;QACX,GAAG,MAAM,kBAAkB;QAE3B,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;QAAc;KAAS;IAE3B,MAAM,oBAAoB,CAAC,KAAyB;QAClD,gBAAgB,CAAA,OAAQ,CAAC;gBACvB,GAAG,IAAI;gBACP,CAAC,IAAI,EAAE;YACT,CAAC;IACH;IAEA,MAAM,eAAe;QACnB,gBAAgB;YACd,OAAO;YACP,UAAU;YACV,MAAM;YACN,cAAc;YACd,WAAW;YACX,QAAQ;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB,aAAa,KAAK,IACzC,aAAa,QAAQ,KAAK,SAC1B,aAAa,IAAI,IACjB,aAAa,YAAY,KAAK,SAC9B,aAAa,SAAS,KAAK;IAE7B,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,8IAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;;;;;kCAEtB,8OAAC;wBACC,MAAK;wBACL,aAAY;wBACZ,OAAO,aAAa,KAAK;wBACzB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;wBAC1D,WAAU;;;;;;oBAEX,2BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;0BAMrB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,SAAS,IAAM,eAAe,CAAC;wBAC/B,WAAU;;0CAEV,8OAAC,8IAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,8OAAC;0CAAK;;;;;;4BACL,kCACC,8OAAC;gCAAK,WAAU;0CAAwD;;;;;;;;;;;;oBAM3E,kCACC,8OAAC;wBACC,SAAS;wBACT,WAAU;;0CAEV,8OAAC,8IAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;0CACf,8OAAC;0CAAK;;;;;;;;;;;;;;;;;;YAMX,6BACC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAChE,8OAAC;gCACC,OAAO,aAAa,QAAQ;gCAC5B,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;gCAC7D,WAAU;;kDAEV,8OAAC;wCAAO,OAAM;kDAAM;;;;;;oCACnB,eAAe,YAAY,IAAI,CAAA,yBAC9B,8OAAC;4CAAsB,OAAO;sDAAW;2CAA5B;;;;;;;;;;;;;;;;;kCAMnB,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAChE,8OAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,OAAO,aAAa,IAAI;gCACxB,UAAU,CAAC,IAAM,kBAAkB,QAAQ,EAAE,MAAM,CAAC,KAAK;gCACzD,WAAU;;;;;;;;;;;;kCAKd,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAChE,8OAAC;gCACC,OAAO,aAAa,YAAY;gCAChC,UAAU,CAAC,IAAM,kBAAkB,gBAAgB,EAAE,MAAM,CAAC,KAAK;gCACjE,WAAU;;kDAEV,8OAAC;wCAAO,OAAM;kDAAM;;;;;;kDACpB,8OAAC;wCAAO,OAAM;kDAAY;;;;;;kDAC1B,8OAAC;wCAAO,OAAM;kDAAW;;;;;;kDACzB,8OAAC;wCAAO,OAAM;kDAAc;;;;;;kDAC5B,8OAAC;wCAAO,OAAM;kDAAc;;;;;;;;;;;;;;;;;;kCAKhC,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAChE,8OAAC;gCACC,OAAO,aAAa,SAAS;gCAC7B,UAAU,CAAC,IAAM,kBAAkB,aAAa,EAAE,MAAM,CAAC,KAAK;gCAC9D,WAAU;;kDAEV,8OAAC;wCAAO,OAAM;kDAAM;;;;;;kDACpB,8OAAC;wCAAO,OAAM;kDAAY;;;;;;kDAC1B,8OAAC;wCAAO,OAAM;kDAAO;;;;;;kDACrB,8OAAC;wCAAO,OAAM;kDAAO;;;;;;kDACrB,8OAAC;wCAAO,OAAM;kDAAO;;;;;;;;;;;;;;;;;;kCAKzB,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAChE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,OAAO,aAAa,MAAM;wCAC1B,UAAU,CAAC,IAAM,kBAAkB,UAAU,EAAE,MAAM,CAAC,KAAK;wCAC3D,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAY;;;;;;0DAC1B,8OAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,8OAAC;gDAAO,OAAM;0DAAW;;;;;;0DACzB,8OAAC;gDAAO,OAAM;0DAAgB;;;;;;;;;;;;kDAEhC,8OAAC;wCACC,OAAO,aAAa,SAAS;wCAC7B,UAAU,CAAC,IAAM,kBAAkB,aAAa,EAAE,MAAM,CAAC,KAAK;wCAC9D,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,8OAAC;gDAAO,OAAM;0DAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpC", "debugId": null}}, {"offset": {"line": 539, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Community%20Tool%20Lending%20Platform/my-tool-lending-app/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useCallback } from 'react';\nimport Link from 'next/link';\nimport RemoveBtn from '@/components/RemoveBtn';\nimport SearchAndFilter from '@/components/SearchAndFilter';\nimport { Hi<PERSON><PERSON>cilAlt, HiLocationMarker, <PERSON><PERSON>ser, <PERSON><PERSON>lock, HiCurrencyDollar } from 'react-icons/hi';\n\ninterface Tool {\n  _id: string;\n  name: string;\n  description: string;\n  category?: string;\n  condition?: string;\n  location?: {\n    city: string;\n    area: string;\n    postalCode?: string;\n  };\n  owner?: {\n    name: string;\n    email: string;\n    phone?: string;\n  };\n  availability?: string;\n  status?: string; // For backward compatibility\n  borrowingTerms?: {\n    maxDuration: number;\n    deposit: number;\n    instructions?: string;\n  };\n  tags?: string[];\n  createdAt: string;\n  updatedAt: string;\n}\n\ninterface SearchParams {\n  query: string;\n  category: string;\n  city: string;\n  availability: string;\n  condition: string;\n  sortBy: string;\n  sortOrder: string;\n}\n\nexport default function Home() {\n  const [tools, setTools] = useState<Tool[]>([]);\n  const [filterOptions, setFilterOptions] = useState<any>({});\n  const [isLoading, setIsLoading] = useState(false);\n  const [totalResults, setTotalResults] = useState(0);\n\n  // Search function\n  const handleSearch = useCallback(async (searchParams: SearchParams) => {\n    setIsLoading(true);\n    try {\n      const queryParams = new URLSearchParams();\n      Object.entries(searchParams).forEach(([key, value]) => {\n        if (value && value !== 'all') {\n          queryParams.append(key, value);\n        }\n      });\n\n      const response = await fetch(`/api/tools/search?${queryParams.toString()}`);\n      const data = await response.json();\n\n      if (response.ok) {\n        setTools(data.tools || []);\n        setFilterOptions(data.filterOptions || {});\n        setTotalResults(data.totalResults || 0);\n      } else {\n        console.error('Search failed:', data.error);\n        setTools([]);\n        setTotalResults(0);\n      }\n    } catch (error) {\n      console.error('Search error:', error);\n      setTools([]);\n      setTotalResults(0);\n    } finally {\n      setIsLoading(false);\n    }\n  }, []);\n\n  // Load initial data\n  useEffect(() => {\n    handleSearch({\n      query: '',\n      category: 'all',\n      city: '',\n      availability: 'all',\n      condition: 'all',\n      sortBy: 'createdAt',\n      sortOrder: 'desc'\n    });\n  }, [handleSearch]);\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\n      <div className=\"container mx-auto px-4 py-8\">\n        {/* Hero Section */}\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-5xl font-bold text-gray-800 mb-4\">\n            Community Tools\n          </h1>\n          <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n            Share tools, build community. Discover what your neighbors have to offer and contribute your own tools to help others.\n          </p>\n        </div>\n\n        {/* Search and Filter */}\n        <SearchAndFilter\n          onSearch={handleSearch}\n          filterOptions={filterOptions}\n          isLoading={isLoading}\n        />\n\n        {/* Results Summary */}\n        <div className=\"mb-6\">\n          <p className=\"text-gray-600\">\n            {isLoading ? 'Searching...' : `Found ${totalResults} tool${totalResults !== 1 ? 's' : ''}`}\n          </p>\n        </div>\n\n        {tools && tools.length > 0 ? (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n            {tools.map((tool) => (\n              <div key={tool._id} className=\"bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 overflow-hidden\">\n                {/* Tool Header */}\n                <div className=\"bg-gradient-to-r from-blue-500 to-purple-600 p-4\">\n                  <div className=\"flex justify-between items-start\">\n                    <h2 className=\"text-xl font-bold text-white truncate flex-1\">{tool.name}</h2>\n                    <span className=\"ml-2 px-2 py-1 bg-white bg-opacity-20 text-white text-xs rounded-full\">\n                      {tool.category}\n                    </span>\n                  </div>\n                  <div className=\"flex items-center mt-2\">\n                    <span className={`px-3 py-1 rounded-full text-xs font-medium ${\n                      (tool.availability || tool.status) === 'available'\n                        ? 'bg-green-100 text-green-800'\n                        : (tool.availability || tool.status) === 'borrowed'\n                        ? 'bg-yellow-100 text-yellow-800'\n                        : 'bg-red-100 text-red-800'\n                    }`}>\n                      {(() => {\n                        const status = tool.availability || tool.status || 'available';\n                        return status.charAt(0).toUpperCase() + status.slice(1);\n                      })()}\n                    </span>\n                  </div>\n                </div>\n\n                {/* Tool Content */}\n                <div className=\"p-6\">\n                  <p className=\"text-gray-600 text-sm leading-relaxed mb-4 line-clamp-3\">\n                    {tool.description}\n                  </p>\n\n                  {/* Tool Details */}\n                  <div className=\"space-y-2 mb-4 text-sm\">\n                    {tool.location && (\n                      <div className=\"flex items-center text-gray-500\">\n                        <HiLocationMarker className=\"h-4 w-4 mr-2\" />\n                        <span>{tool.location.city}, {tool.location.area}</span>\n                      </div>\n                    )}\n\n                    {tool.owner && (\n                      <div className=\"flex items-center text-gray-500\">\n                        <HiUser className=\"h-4 w-4 mr-2\" />\n                        <span>{tool.owner.name}</span>\n                      </div>\n                    )}\n\n                    {tool.condition && (\n                      <div className=\"flex items-center text-gray-500\">\n                        <span className=\"text-xs bg-gray-100 px-2 py-1 rounded\">\n                          Condition: {tool.condition}\n                        </span>\n                      </div>\n                    )}\n\n                    {tool.borrowingTerms && (\n                      <div className=\"flex items-center justify-between text-gray-500\">\n                        <div className=\"flex items-center\">\n                          <HiClock className=\"h-4 w-4 mr-1\" />\n                          <span className=\"text-xs\">Max {tool.borrowingTerms.maxDuration} days</span>\n                        </div>\n                        {tool.borrowingTerms.deposit > 0 && (\n                          <div className=\"flex items-center\">\n                            <HiCurrencyDollar className=\"h-4 w-4 mr-1\" />\n                            <span className=\"text-xs\">LKR {tool.borrowingTerms.deposit}</span>\n                          </div>\n                        )}\n                      </div>\n                    )}\n                  </div>\n\n                  {/* Tags */}\n                  {tool.tags && tool.tags.length > 0 && (\n                    <div className=\"mb-4\">\n                      <div className=\"flex flex-wrap gap-1\">\n                        {tool.tags.slice(0, 3).map((tag, index) => (\n                          <span key={index} className=\"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded\">\n                            {tag}\n                          </span>\n                        ))}\n                        {tool.tags.length > 3 && (\n                          <span className=\"text-xs text-gray-500\">+{tool.tags.length - 3} more</span>\n                        )}\n                      </div>\n                    </div>\n                  )}\n\n                  {/* Action Buttons */}\n                  <div className=\"flex justify-between items-center pt-4 border-t border-gray-100\">\n                    <div className=\"flex gap-3\">\n                      <Link\n                        href={`/edit-tool/${tool._id}`}\n                        className=\"flex items-center justify-center w-10 h-10 bg-blue-100 text-blue-600 rounded-full hover:bg-blue-200 transition-colors duration-200\"\n                        title=\"Edit Tool\"\n                      >\n                        <HiPencilAlt size={18} />\n                      </Link>\n                      <div className=\"flex items-center justify-center w-10 h-10 bg-red-100 text-red-600 rounded-full hover:bg-red-200 transition-colors duration-200\">\n                        <RemoveBtn id={tool._id} />\n                      </div>\n                    </div>\n                    <div className=\"text-xs text-gray-400\">\n                      {tool.createdAt ? new Date(tool.createdAt).toLocaleDateString() : 'Recently added'}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        ) : !isLoading ? (\n          <div className=\"text-center py-16\">\n            <div className=\"bg-white rounded-2xl shadow-lg p-12 max-w-md mx-auto\">\n              <div className=\"w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6\">\n                <svg className=\"w-12 h-12 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\" />\n                </svg>\n              </div>\n              <h3 className=\"text-2xl font-bold text-gray-800 mb-4\">No tools found</h3>\n              <p className=\"text-gray-600 mb-6\">Try adjusting your search criteria or be the first to add a tool!</p>\n              <Link\n                href=\"/add-tool\"\n                className=\"inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-semibold rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all duration-200 transform hover:scale-105\"\n              >\n                Add Your First Tool\n              </Link>\n            </div>\n          </div>\n        ) : null}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AA8Ce,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO,CAAC;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,kBAAkB;IAClB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACtC,aAAa;QACb,IAAI;YACF,MAAM,cAAc,IAAI;YACxB,OAAO,OAAO,CAAC,cAAc,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAChD,IAAI,SAAS,UAAU,OAAO;oBAC5B,YAAY,MAAM,CAAC,KAAK;gBAC1B;YACF;YAEA,MAAM,WAAW,MAAM,MAAM,CAAC,kBAAkB,EAAE,YAAY,QAAQ,IAAI;YAC1E,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,SAAS,EAAE,EAAE;gBACf,SAAS,KAAK,KAAK,IAAI,EAAE;gBACzB,iBAAiB,KAAK,aAAa,IAAI,CAAC;gBACxC,gBAAgB,KAAK,YAAY,IAAI;YACvC,OAAO;gBACL,QAAQ,KAAK,CAAC,kBAAkB,KAAK,KAAK;gBAC1C,SAAS,EAAE;gBACX,gBAAgB;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,SAAS,EAAE;YACX,gBAAgB;QAClB,SAAU;YACR,aAAa;QACf;IACF,GAAG,EAAE;IAEL,oBAAoB;IACpB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa;YACX,OAAO;YACP,UAAU;YACV,MAAM;YACN,cAAc;YACd,WAAW;YACX,QAAQ;YACR,WAAW;QACb;IACF,GAAG;QAAC;KAAa;IAEjB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;8BAMzD,8OAAC,qIAAA,CAAA,UAAe;oBACd,UAAU;oBACV,eAAe;oBACf,WAAW;;;;;;8BAIb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCACV,YAAY,iBAAiB,CAAC,MAAM,EAAE,aAAa,KAAK,EAAE,iBAAiB,IAAI,MAAM,IAAI;;;;;;;;;;;gBAI7F,SAAS,MAAM,MAAM,GAAG,kBACvB,8OAAC;oBAAI,WAAU;8BACZ,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;4BAAmB,WAAU;;8CAE5B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAgD,KAAK,IAAI;;;;;;8DACvE,8OAAC;oDAAK,WAAU;8DACb,KAAK,QAAQ;;;;;;;;;;;;sDAGlB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAW,CAAC,2CAA2C,EAC3D,CAAC,KAAK,YAAY,IAAI,KAAK,MAAM,MAAM,cACnC,gCACA,CAAC,KAAK,YAAY,IAAI,KAAK,MAAM,MAAM,aACvC,kCACA,2BACJ;0DACC,CAAC;oDACA,MAAM,SAAS,KAAK,YAAY,IAAI,KAAK,MAAM,IAAI;oDACnD,OAAO,OAAO,MAAM,CAAC,GAAG,WAAW,KAAK,OAAO,KAAK,CAAC;gDACvD,CAAC;;;;;;;;;;;;;;;;;8CAMP,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDACV,KAAK,WAAW;;;;;;sDAInB,8OAAC;4CAAI,WAAU;;gDACZ,KAAK,QAAQ,kBACZ,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,8IAAA,CAAA,mBAAgB;4DAAC,WAAU;;;;;;sEAC5B,8OAAC;;gEAAM,KAAK,QAAQ,CAAC,IAAI;gEAAC;gEAAG,KAAK,QAAQ,CAAC,IAAI;;;;;;;;;;;;;gDAIlD,KAAK,KAAK,kBACT,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,8IAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,8OAAC;sEAAM,KAAK,KAAK,CAAC,IAAI;;;;;;;;;;;;gDAIzB,KAAK,SAAS,kBACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;;4DAAwC;4DAC1C,KAAK,SAAS;;;;;;;;;;;;gDAK/B,KAAK,cAAc,kBAClB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,8IAAA,CAAA,UAAO;oEAAC,WAAU;;;;;;8EACnB,8OAAC;oEAAK,WAAU;;wEAAU;wEAAK,KAAK,cAAc,CAAC,WAAW;wEAAC;;;;;;;;;;;;;wDAEhE,KAAK,cAAc,CAAC,OAAO,GAAG,mBAC7B,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,8IAAA,CAAA,mBAAgB;oEAAC,WAAU;;;;;;8EAC5B,8OAAC;oEAAK,WAAU;;wEAAU;wEAAK,KAAK,cAAc,CAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;wCAQnE,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,mBAC/B,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;oDACZ,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,KAAK,sBAC/B,8OAAC;4DAAiB,WAAU;sEACzB;2DADQ;;;;;oDAIZ,KAAK,IAAI,CAAC,MAAM,GAAG,mBAClB,8OAAC;wDAAK,WAAU;;4DAAwB;4DAAE,KAAK,IAAI,CAAC,MAAM,GAAG;4DAAE;;;;;;;;;;;;;;;;;;sDAOvE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,4JAAA,CAAA,UAAI;4DACH,MAAM,CAAC,WAAW,EAAE,KAAK,GAAG,EAAE;4DAC9B,WAAU;4DACV,OAAM;sEAEN,cAAA,8OAAC,8IAAA,CAAA,cAAW;gEAAC,MAAM;;;;;;;;;;;sEAErB,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,+HAAA,CAAA,UAAS;gEAAC,IAAI,KAAK,GAAG;;;;;;;;;;;;;;;;;8DAG3B,8OAAC;oDAAI,WAAU;8DACZ,KAAK,SAAS,GAAG,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB,KAAK;;;;;;;;;;;;;;;;;;;2BAtGhE,KAAK,GAAG;;;;;;;;;2BA6GpB,CAAC,0BACH,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;oCAA0B,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACjF,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;0CAGzE,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAClC,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;2BAKH;;;;;;;;;;;;AAIZ", "debugId": null}}]}