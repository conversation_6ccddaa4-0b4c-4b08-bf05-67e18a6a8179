#!/usr/bin/env node

/**
 * Test Data Seeding Script
 * 
 * This script seeds the database with sample tool data for testing purposes.
 */

import { config } from 'dotenv';
import path from 'path';
import connectMongoDB, { disconnectMongoDB } from '../lib/mongodb';
import Tool from '../models/tool';

// Load environment variables
config({ path: path.resolve(process.cwd(), '.env.local') });

const sampleTools = [
  {
    name: "Electric Drill",
    description: "Cordless electric drill with multiple bits. Perfect for home improvement projects.",
    status: "available"
  },
  {
    name: "Circular Saw",
    description: "Professional-grade circular saw for cutting wood and other materials.",
    status: "available"
  },
  {
    name: "Hammer",
    description: "Heavy-duty claw hammer for construction and repair work.",
    status: "borrowed"
  },
  {
    name: "Level",
    description: "4-foot aluminum level for ensuring straight and level installations.",
    status: "available"
  },
  {
    name: "Socket Set",
    description: "Complete socket wrench set with metric and standard sizes.",
    status: "available"
  },
  {
    name: "Ladder",
    description: "6-foot step ladder, perfect for reaching high places safely.",
    status: "maintenance"
  },
  {
    name: "Pressure Washer",
    description: "Electric pressure washer for cleaning driveways, decks, and siding.",
    status: "available"
  },
  {
    name: "Tile Saw",
    description: "Wet tile saw for precise ceramic and stone tile cutting.",
    status: "borrowed"
  },
  {
    name: "Multimeter",
    description: "Digital multimeter for electrical testing and troubleshooting.",
    status: "available"
  },
  {
    name: "Chainsaw",
    description: "Gas-powered chainsaw for tree trimming and firewood cutting.",
    status: "available"
  }
];

async function seedDatabase() {
  console.log("🌱 Community Tool Lending Platform - Database Seeding");
  console.log("=" * 60);
  console.log(`📅 Seeding started at: ${new Date().toISOString()}`);
  console.log(`🔗 MongoDB URI: ${process.env.MONGODB_URI ? 'Configured ✅' : 'Missing ❌'}`);
  console.log("=" * 60);

  if (!process.env.MONGODB_URI) {
    console.error("❌ MONGODB_URI environment variable is not set!");
    console.error("Please check your .env.local file.");
    process.exit(1);
  }

  try {
    // Connect to database
    console.log("🔌 Connecting to MongoDB...");
    await connectMongoDB();
    console.log("✅ Connected successfully!");

    // Check existing data
    console.log("\n📊 Checking existing data...");
    const existingCount = await Tool.countDocuments();
    console.log(`📈 Current tools in database: ${existingCount}`);

    if (existingCount > 0) {
      console.log("\n⚠️ Database already contains tools.");
      console.log("Do you want to:");
      console.log("1. Add new tools alongside existing ones");
      console.log("2. Clear existing tools and add fresh data");
      console.log("3. Cancel seeding");
      
      // For automated testing, we'll add alongside existing data
      console.log("🔄 Adding new tools alongside existing ones...");
    }

    // Insert sample tools
    console.log("\n🔧 Inserting sample tools...");
    const insertedTools = [];
    
    for (let i = 0; i < sampleTools.length; i++) {
      const toolData = sampleTools[i];
      console.log(`📝 Creating tool ${i + 1}/${sampleTools.length}: ${toolData.name}`);
      
      try {
        const tool = await Tool.create(toolData);
        insertedTools.push(tool);
        console.log(`   ✅ Created with ID: ${tool._id}`);
      } catch (error) {
        console.log(`   ❌ Failed to create: ${error}`);
      }
    }

    // Verify insertion
    console.log("\n🔍 Verifying inserted data...");
    const newCount = await Tool.countDocuments();
    console.log(`📈 Total tools in database: ${newCount}`);
    console.log(`➕ Tools added in this session: ${insertedTools.length}`);

    // Display sample of inserted tools
    console.log("\n📋 Sample of inserted tools:");
    const sampleInserted = await Tool.find().limit(5).sort({ createdAt: -1 });
    sampleInserted.forEach((tool, index) => {
      console.log(`${index + 1}. ${tool.name} (${tool.status}) - ${tool.description.substring(0, 50)}...`);
    });

    // Test different status counts
    console.log("\n📊 Tools by status:");
    const statusCounts = await Tool.aggregate([
      { $group: { _id: "$status", count: { $sum: 1 } } },
      { $sort: { _id: 1 } }
    ]);
    
    statusCounts.forEach(status => {
      console.log(`   ${status._id}: ${status.count} tools`);
    });

    console.log("\n🎉 Database seeding completed successfully!");
    console.log("✅ Sample data is now available for testing.");
    
    return {
      success: true,
      totalTools: newCount,
      addedTools: insertedTools.length,
      statusBreakdown: statusCounts
    };

  } catch (error) {
    console.error("\n💥 Error during database seeding:");
    console.error(error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    };
  } finally {
    try {
      await disconnectMongoDB();
      console.log("🔌 Disconnected from MongoDB.");
    } catch (error) {
      console.error("Error during cleanup:", error);
    }
  }
}

async function clearDatabase() {
  console.log("🗑️ Clearing all tools from database...");
  
  try {
    await connectMongoDB();
    const deleteResult = await Tool.deleteMany({});
    console.log(`✅ Deleted ${deleteResult.deletedCount} tools from database.`);
    return deleteResult.deletedCount;
  } catch (error) {
    console.error("❌ Error clearing database:", error);
    throw error;
  } finally {
    await disconnectMongoDB();
  }
}

// Handle command line arguments
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--clear')) {
    await clearDatabase();
  } else if (args.includes('--help')) {
    console.log("Database Seeding Script");
    console.log("Usage:");
    console.log("  npm run seed-db          # Seed database with sample data");
    console.log("  npm run seed-db --clear  # Clear all tools from database");
    console.log("  npm run seed-db --help   # Show this help message");
  } else {
    const result = await seedDatabase();
    process.exit(result.success ? 0 : 1);
  }
}

// Handle process termination gracefully
process.on('SIGINT', async () => {
  console.log('\n🛑 Seeding interrupted by user');
  try {
    await disconnectMongoDB();
  } catch (error) {
    console.error("Error during cleanup:", error);
  }
  process.exit(1);
});

// Run the main function
if (require.main === module) {
  main().catch((error) => {
    console.error("Fatal error:", error);
    process.exit(1);
  });
}

export { seedDatabase, clearDatabase };
