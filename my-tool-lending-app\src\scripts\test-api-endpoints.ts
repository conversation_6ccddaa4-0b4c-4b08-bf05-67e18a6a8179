#!/usr/bin/env node

/**
 * API Endpoints Test Script
 * 
 * This script tests the API endpoints without requiring database connection.
 * Useful for testing application structure and routing.
 */

import { config } from 'dotenv';
import path from 'path';

// Load environment variables
config({ path: path.resolve(process.cwd(), '.env.local') });

async function testAPIStructure() {
  console.log("🔧 Community Tool Lending Platform - API Structure Test");
  console.log("=" * 60);
  console.log(`📅 Test started at: ${new Date().toISOString()}`);
  console.log("=" * 60);

  const tests = [
    {
      name: "MongoDB Connection Module",
      test: async () => {
        try {
          const { getConnectionStatus } = await import('../lib/mongodb');
          const status = getConnectionStatus();
          return {
            success: true,
            details: { 
              module: "loaded",
              initialState: status
            }
          };
        } catch (error) {
          return {
            success: false,
            details: { error: error instanceof Error ? error.message : String(error) }
          };
        }
      }
    },
    {
      name: "Tool Model",
      test: async () => {
        try {
          const Tool = await import('../models/tool');
          return {
            success: true,
            details: { 
              module: "loaded",
              hasDefault: !!Tool.default,
              modelName: Tool.default?.modelName || "unknown"
            }
          };
        } catch (error) {
          return {
            success: false,
            details: { error: error instanceof Error ? error.message : String(error) }
          };
        }
      }
    },
    {
      name: "Database Tester Module",
      test: async () => {
        try {
          const DatabaseTester = await import('../lib/db-test');
          const tester = new DatabaseTester.default();
          return {
            success: true,
            details: { 
              module: "loaded",
              hasDefault: !!DatabaseTester.default,
              instance: "created"
            }
          };
        } catch (error) {
          return {
            success: false,
            details: { error: error instanceof Error ? error.message : String(error) }
          };
        }
      }
    },
    {
      name: "Environment Configuration",
      test: async () => {
        const mongoUri = process.env.MONGODB_URI;
        const nodeEnv = process.env.NODE_ENV;
        
        return {
          success: !!mongoUri,
          details: {
            mongoUri: mongoUri ? "configured" : "missing",
            nodeEnv: nodeEnv || "not set",
            envVarsCount: Object.keys(process.env).length
          }
        };
      }
    }
  ];

  console.log("🧪 Running API structure tests...\n");

  const results = [];
  for (const test of tests) {
    console.log(`🔍 Testing: ${test.name}`);
    try {
      const result = await test.test();
      results.push({
        name: test.name,
        ...result,
        timestamp: new Date()
      });
      
      if (result.success) {
        console.log(`   ✅ PASSED`);
        if (result.details) {
          console.log(`   📋 Details: ${JSON.stringify(result.details, null, 6)}`);
        }
      } else {
        console.log(`   ❌ FAILED`);
        if (result.details) {
          console.log(`   📋 Details: ${JSON.stringify(result.details, null, 6)}`);
        }
      }
    } catch (error) {
      results.push({
        name: test.name,
        success: false,
        details: { error: error instanceof Error ? error.message : String(error) },
        timestamp: new Date()
      });
      console.log(`   ❌ FAILED with exception`);
      console.log(`   📋 Error: ${error}`);
    }
    console.log("");
  }

  // Summary
  console.log("=" * 60);
  console.log("📋 API STRUCTURE TEST SUMMARY");
  console.log("=" * 60);
  
  const passed = results.filter(r => r.success).length;
  const failed = results.filter(r => !r.success).length;
  
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📊 Total: ${results.length}`);
  
  if (failed > 0) {
    console.log("\n❌ Failed Tests:");
    results.filter(r => !r.success).forEach((result, index) => {
      console.log(`${index + 1}. ${result.name}`);
      if (result.details) {
        console.log(`   Details: ${JSON.stringify(result.details, null, 2)}`);
      }
    });
  }
  
  console.log("=" * 60);
  
  return {
    success: passed === results.length,
    results,
    summary: { total: results.length, passed, failed }
  };
}

// Run the test
if (require.main === module) {
  testAPIStructure()
    .then((result) => {
      process.exit(result.success ? 0 : 1);
    })
    .catch((error) => {
      console.error("Fatal error:", error);
      process.exit(1);
    });
}

export default testAPIStructure;
