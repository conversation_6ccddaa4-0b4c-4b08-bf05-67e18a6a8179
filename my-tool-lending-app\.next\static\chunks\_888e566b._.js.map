{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Community%20Tool%20Lending%20Platform/my-tool-lending-app/src/app/add-tool/page.tsx"], "sourcesContent": ["'use client'; // This marks the component as a Client Component\n\nimport { useState } from 'react';\nimport { useRouter } from 'next/navigation';\n\nexport default function AddToolPage() {\n  const [name, setName] = useState('');\n  const [description, setDescription] = useState('');\n  const [category, setCategory] = useState('Other');\n  const [condition, setCondition] = useState('Good');\n  const [city, setCity] = useState('');\n  const [area, setArea] = useState('');\n  const [postalCode, setPostalCode] = useState('');\n  const [ownerName, setOwnerName] = useState('');\n  const [ownerEmail, setOwnerEmail] = useState('');\n  const [ownerPhone, setOwnerPhone] = useState('');\n  const [maxDuration, setMaxDuration] = useState(7);\n  const [deposit, setDeposit] = useState(0);\n  const [instructions, setInstructions] = useState('');\n  const [tags, setTags] = useState('');\n  const router = useRouter();\n\n  const categories = [\n    'Power Tools', 'Hand Tools', 'Garden Tools', 'Automotive',\n    'Construction', 'Electrical', 'Plumbing', 'Cleaning',\n    'Kitchen Appliances', 'Sports & Recreation', 'Other'\n  ];\n\n  const conditions = ['Excellent', 'Good', 'Fair', 'Poor'];\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    // Validation\n    if (!name || !description || !city || !area || !ownerName || !ownerEmail) {\n      alert('Please fill in all required fields.');\n      return;\n    }\n\n    const toolData = {\n      name,\n      description,\n      category,\n      condition,\n      location: {\n        city,\n        area,\n        postalCode\n      },\n      owner: {\n        name: ownerName,\n        email: ownerEmail,\n        phone: ownerPhone\n      },\n      borrowingTerms: {\n        maxDuration: Number(maxDuration),\n        deposit: Number(deposit),\n        instructions\n      },\n      tags: tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0)\n    };\n\n    try {\n      const res = await fetch('/api/tools', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(toolData),\n      });\n\n      if (res.ok) {\n        alert('Tool added successfully!');\n        router.push('/'); // Redirect to homepage on success\n        router.refresh(); // Tell Next.js to refresh the data on the homepage\n      } else {\n        const errorData = await res.json();\n        throw new Error(errorData.error || 'Failed to create a tool');\n      }\n    } catch (error) {\n      console.log(error);\n      alert('Error creating tool: ' + error);\n    }\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-12\">\n      <div className=\"max-w-lg mx-auto px-4\">\n        {/* Header */}\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-4xl font-bold text-gray-800 mb-4\">Add a New Tool</h1>\n          <p className=\"text-gray-600\">Share your tools with the community and help your neighbors</p>\n        </div>\n\n        {/* Form */}\n        <div className=\"bg-white rounded-2xl shadow-xl p-8\">\n          <form onSubmit={handleSubmit} className=\"space-y-6\">\n            {/* Basic Information */}\n            <div className=\"border-b border-gray-200 pb-6\">\n              <h3 className=\"text-lg font-semibold text-gray-800 mb-4\">Basic Information</h3>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <label htmlFor=\"name\" className=\"block text-sm font-semibold text-gray-700 mb-2\">\n                    Tool Name *\n                  </label>\n                  <input\n                    id=\"name\"\n                    type=\"text\"\n                    onChange={(e) => setName(e.target.value)}\n                    value={name}\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200\"\n                    placeholder=\"e.g., Electric Drill, Lawn Mower\"\n                    required\n                  />\n                </div>\n\n                <div>\n                  <label htmlFor=\"category\" className=\"block text-sm font-semibold text-gray-700 mb-2\">\n                    Category *\n                  </label>\n                  <select\n                    id=\"category\"\n                    onChange={(e) => setCategory(e.target.value)}\n                    value={category}\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200\"\n                    required\n                  >\n                    {categories.map(cat => (\n                      <option key={cat} value={cat}>{cat}</option>\n                    ))}\n                  </select>\n                </div>\n              </div>\n\n              <div className=\"mt-4\">\n                <label htmlFor=\"description\" className=\"block text-sm font-semibold text-gray-700 mb-2\">\n                  Description *\n                </label>\n                <textarea\n                  id=\"description\"\n                  onChange={(e) => setDescription(e.target.value)}\n                  value={description}\n                  rows={3}\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 resize-none\"\n                  placeholder=\"Describe your tool, its condition, and any special features...\"\n                  required\n                />\n              </div>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mt-4\">\n                <div>\n                  <label htmlFor=\"condition\" className=\"block text-sm font-semibold text-gray-700 mb-2\">\n                    Condition\n                  </label>\n                  <select\n                    id=\"condition\"\n                    onChange={(e) => setCondition(e.target.value)}\n                    value={condition}\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200\"\n                  >\n                    {conditions.map(cond => (\n                      <option key={cond} value={cond}>{cond}</option>\n                    ))}\n                  </select>\n                </div>\n\n                <div>\n                  <label htmlFor=\"tags\" className=\"block text-sm font-semibold text-gray-700 mb-2\">\n                    Tags (comma separated)\n                  </label>\n                  <input\n                    id=\"tags\"\n                    type=\"text\"\n                    onChange={(e) => setTags(e.target.value)}\n                    value={tags}\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200\"\n                    placeholder=\"e.g., cordless, 18V, battery\"\n                  />\n                </div>\n              </div>\n            </div>\n\n            {/* Location Information */}\n            <div className=\"border-b border-gray-200 pb-6\">\n              <h3 className=\"text-lg font-semibold text-gray-800 mb-4\">Location</h3>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                <div>\n                  <label htmlFor=\"city\" className=\"block text-sm font-semibold text-gray-700 mb-2\">\n                    City *\n                  </label>\n                  <input\n                    id=\"city\"\n                    type=\"text\"\n                    onChange={(e) => setCity(e.target.value)}\n                    value={city}\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200\"\n                    placeholder=\"e.g., Colombo\"\n                    required\n                  />\n                </div>\n\n                <div>\n                  <label htmlFor=\"area\" className=\"block text-sm font-semibold text-gray-700 mb-2\">\n                    Area/District *\n                  </label>\n                  <input\n                    id=\"area\"\n                    type=\"text\"\n                    onChange={(e) => setArea(e.target.value)}\n                    value={area}\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200\"\n                    placeholder=\"e.g., Nugegoda\"\n                    required\n                  />\n                </div>\n\n                <div>\n                  <label htmlFor=\"postalCode\" className=\"block text-sm font-semibold text-gray-700 mb-2\">\n                    Postal Code\n                  </label>\n                  <input\n                    id=\"postalCode\"\n                    type=\"text\"\n                    onChange={(e) => setPostalCode(e.target.value)}\n                    value={postalCode}\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200\"\n                    placeholder=\"e.g., 10250\"\n                  />\n                </div>\n              </div>\n            </div>\n\n            {/* Owner Information */}\n            <div className=\"border-b border-gray-200 pb-6\">\n              <h3 className=\"text-lg font-semibold text-gray-800 mb-4\">Contact Information</h3>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <label htmlFor=\"ownerName\" className=\"block text-sm font-semibold text-gray-700 mb-2\">\n                    Your Name *\n                  </label>\n                  <input\n                    id=\"ownerName\"\n                    type=\"text\"\n                    onChange={(e) => setOwnerName(e.target.value)}\n                    value={ownerName}\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200\"\n                    placeholder=\"Your full name\"\n                    required\n                  />\n                </div>\n\n                <div>\n                  <label htmlFor=\"ownerEmail\" className=\"block text-sm font-semibold text-gray-700 mb-2\">\n                    Email Address *\n                  </label>\n                  <input\n                    id=\"ownerEmail\"\n                    type=\"email\"\n                    onChange={(e) => setOwnerEmail(e.target.value)}\n                    value={ownerEmail}\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200\"\n                    placeholder=\"<EMAIL>\"\n                    required\n                  />\n                </div>\n              </div>\n\n              <div className=\"mt-4\">\n                <label htmlFor=\"ownerPhone\" className=\"block text-sm font-semibold text-gray-700 mb-2\">\n                  Phone Number (Optional)\n                </label>\n                <input\n                  id=\"ownerPhone\"\n                  type=\"tel\"\n                  onChange={(e) => setOwnerPhone(e.target.value)}\n                  value={ownerPhone}\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200\"\n                  placeholder=\"+94 77 123 4567\"\n                />\n              </div>\n            </div>\n\n            {/* Borrowing Terms */}\n            <div className=\"pb-6\">\n              <h3 className=\"text-lg font-semibold text-gray-800 mb-4\">Borrowing Terms</h3>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <label htmlFor=\"maxDuration\" className=\"block text-sm font-semibold text-gray-700 mb-2\">\n                    Maximum Loan Duration (days)\n                  </label>\n                  <input\n                    id=\"maxDuration\"\n                    type=\"number\"\n                    min=\"1\"\n                    max=\"30\"\n                    onChange={(e) => setMaxDuration(Number(e.target.value))}\n                    value={maxDuration}\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200\"\n                  />\n                </div>\n\n                <div>\n                  <label htmlFor=\"deposit\" className=\"block text-sm font-semibold text-gray-700 mb-2\">\n                    Security Deposit (LKR)\n                  </label>\n                  <input\n                    id=\"deposit\"\n                    type=\"number\"\n                    min=\"0\"\n                    onChange={(e) => setDeposit(Number(e.target.value))}\n                    value={deposit}\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200\"\n                    placeholder=\"0\"\n                  />\n                </div>\n              </div>\n\n              <div className=\"mt-4\">\n                <label htmlFor=\"instructions\" className=\"block text-sm font-semibold text-gray-700 mb-2\">\n                  Special Instructions\n                </label>\n                <textarea\n                  id=\"instructions\"\n                  onChange={(e) => setInstructions(e.target.value)}\n                  value={instructions}\n                  rows={3}\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 resize-none\"\n                  placeholder=\"Any special care instructions, pickup arrangements, etc.\"\n                />\n              </div>\n            </div>\n\n            <div className=\"pt-4\">\n              <button\n                type=\"submit\"\n                className=\"w-full py-4 px-6 bg-gradient-to-r from-blue-500 to-purple-600 text-white font-bold rounded-lg hover:from-blue-600 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 transform hover:scale-105 shadow-lg\"\n              >\n                Add Tool to Community\n              </button>\n            </div>\n          </form>\n\n          {/* Help Text */}\n          <div className=\"mt-6 p-4 bg-blue-50 rounded-lg\">\n            <p className=\"text-sm text-blue-800\">\n              <strong>Tip:</strong> Be descriptive! Include details like brand, model, condition, and any accessories to help community members understand what you're sharing.\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA,cAAc,iDAAiD;;;AAKhD,SAAS;;IACtB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,aAAa;QACjB;QAAe;QAAc;QAAgB;QAC7C;QAAgB;QAAc;QAAY;QAC1C;QAAsB;QAAuB;KAC9C;IAED,MAAM,aAAa;QAAC;QAAa;QAAQ;QAAQ;KAAO;IAExD,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,aAAa;QACb,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,aAAa,CAAC,YAAY;YACxE,MAAM;YACN;QACF;QAEA,MAAM,WAAW;YACf;YACA;YACA;YACA;YACA,UAAU;gBACR;gBACA;gBACA;YACF;YACA,OAAO;gBACL,MAAM;gBACN,OAAO;gBACP,OAAO;YACT;YACA,gBAAgB;gBACd,aAAa,OAAO;gBACpB,SAAS,OAAO;gBAChB;YACF;YACA,MAAM,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,MAAO,IAAI,IAAI,IAAI,MAAM,CAAC,CAAA,MAAO,IAAI,MAAM,GAAG;QAC1E;QAEA,IAAI;YACF,MAAM,MAAM,MAAM,MAAM,cAAc;gBACpC,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,IAAI,EAAE,EAAE;gBACV,MAAM;gBACN,OAAO,IAAI,CAAC,MAAM,kCAAkC;gBACpD,OAAO,OAAO,IAAI,mDAAmD;YACvE,OAAO;gBACL,MAAM,YAAY,MAAM,IAAI,IAAI;gBAChC,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI;YACrC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC;YACZ,MAAM,0BAA0B;QAClC;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;8BAI/B,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAK,UAAU;4BAAc,WAAU;;8CAEtC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDAEzD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAM,SAAQ;4DAAO,WAAU;sEAAiD;;;;;;sEAGjF,6LAAC;4DACC,IAAG;4DACH,MAAK;4DACL,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;4DACvC,OAAO;4DACP,WAAU;4DACV,aAAY;4DACZ,QAAQ;;;;;;;;;;;;8DAIZ,6LAAC;;sEACC,6LAAC;4DAAM,SAAQ;4DAAW,WAAU;sEAAiD;;;;;;sEAGrF,6LAAC;4DACC,IAAG;4DACH,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;4DAC3C,OAAO;4DACP,WAAU;4DACV,QAAQ;sEAEP,WAAW,GAAG,CAAC,CAAA,oBACd,6LAAC;oEAAiB,OAAO;8EAAM;mEAAlB;;;;;;;;;;;;;;;;;;;;;;sDAMrB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,SAAQ;oDAAc,WAAU;8DAAiD;;;;;;8DAGxF,6LAAC;oDACC,IAAG;oDACH,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oDAC9C,OAAO;oDACP,MAAM;oDACN,WAAU;oDACV,aAAY;oDACZ,QAAQ;;;;;;;;;;;;sDAIZ,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAM,SAAQ;4DAAY,WAAU;sEAAiD;;;;;;sEAGtF,6LAAC;4DACC,IAAG;4DACH,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;4DAC5C,OAAO;4DACP,WAAU;sEAET,WAAW,GAAG,CAAC,CAAA,qBACd,6LAAC;oEAAkB,OAAO;8EAAO;mEAApB;;;;;;;;;;;;;;;;8DAKnB,6LAAC;;sEACC,6LAAC;4DAAM,SAAQ;4DAAO,WAAU;sEAAiD;;;;;;sEAGjF,6LAAC;4DACC,IAAG;4DACH,MAAK;4DACL,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;4DACvC,OAAO;4DACP,WAAU;4DACV,aAAY;;;;;;;;;;;;;;;;;;;;;;;;8CAOpB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDAEzD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAM,SAAQ;4DAAO,WAAU;sEAAiD;;;;;;sEAGjF,6LAAC;4DACC,IAAG;4DACH,MAAK;4DACL,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;4DACvC,OAAO;4DACP,WAAU;4DACV,aAAY;4DACZ,QAAQ;;;;;;;;;;;;8DAIZ,6LAAC;;sEACC,6LAAC;4DAAM,SAAQ;4DAAO,WAAU;sEAAiD;;;;;;sEAGjF,6LAAC;4DACC,IAAG;4DACH,MAAK;4DACL,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;4DACvC,OAAO;4DACP,WAAU;4DACV,aAAY;4DACZ,QAAQ;;;;;;;;;;;;8DAIZ,6LAAC;;sEACC,6LAAC;4DAAM,SAAQ;4DAAa,WAAU;sEAAiD;;;;;;sEAGvF,6LAAC;4DACC,IAAG;4DACH,MAAK;4DACL,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4DAC7C,OAAO;4DACP,WAAU;4DACV,aAAY;;;;;;;;;;;;;;;;;;;;;;;;8CAOpB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDAEzD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAM,SAAQ;4DAAY,WAAU;sEAAiD;;;;;;sEAGtF,6LAAC;4DACC,IAAG;4DACH,MAAK;4DACL,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;4DAC5C,OAAO;4DACP,WAAU;4DACV,aAAY;4DACZ,QAAQ;;;;;;;;;;;;8DAIZ,6LAAC;;sEACC,6LAAC;4DAAM,SAAQ;4DAAa,WAAU;sEAAiD;;;;;;sEAGvF,6LAAC;4DACC,IAAG;4DACH,MAAK;4DACL,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4DAC7C,OAAO;4DACP,WAAU;4DACV,aAAY;4DACZ,QAAQ;;;;;;;;;;;;;;;;;;sDAKd,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,SAAQ;oDAAa,WAAU;8DAAiD;;;;;;8DAGvF,6LAAC;oDACC,IAAG;oDACH,MAAK;oDACL,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oDAC7C,OAAO;oDACP,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;8CAMlB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDAEzD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAM,SAAQ;4DAAc,WAAU;sEAAiD;;;;;;sEAGxF,6LAAC;4DACC,IAAG;4DACH,MAAK;4DACL,KAAI;4DACJ,KAAI;4DACJ,UAAU,CAAC,IAAM,eAAe,OAAO,EAAE,MAAM,CAAC,KAAK;4DACrD,OAAO;4DACP,WAAU;;;;;;;;;;;;8DAId,6LAAC;;sEACC,6LAAC;4DAAM,SAAQ;4DAAU,WAAU;sEAAiD;;;;;;sEAGpF,6LAAC;4DACC,IAAG;4DACH,MAAK;4DACL,KAAI;4DACJ,UAAU,CAAC,IAAM,WAAW,OAAO,EAAE,MAAM,CAAC,KAAK;4DACjD,OAAO;4DACP,WAAU;4DACV,aAAY;;;;;;;;;;;;;;;;;;sDAKlB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,SAAQ;oDAAe,WAAU;8DAAiD;;;;;;8DAGzF,6LAAC;oDACC,IAAG;oDACH,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;oDAC/C,OAAO;oDACP,MAAM;oDACN,WAAU;oDACV,aAAY;;;;;;;;;;;;;;;;;;8CAKlB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;sCAOL,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;;kDACX,6LAAC;kDAAO;;;;;;oCAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnC;GA/VwB;;QAeP,qIAAA,CAAA,YAAS;;;KAfF", "debugId": null}}, {"offset": {"line": 764, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Community%20Tool%20Lending%20Platform/my-tool-lending-app/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"next/dist/compiled/react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,yBAAyB,IAAI;QACpC,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,eAAe,OAAO,MACxB,OAAO,KAAK,QAAQ,KAAK,yBACrB,OACA,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI;QACvC,IAAI,aAAa,OAAO,MAAM,OAAO;QACrC,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;QACA,IAAI,aAAa,OAAO,MACtB,OACG,aAAa,OAAO,KAAK,GAAG,IAC3B,QAAQ,KAAK,CACX,sHAEJ,KAAK,QAAQ;YAEb,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,CAAC,KAAK,WAAW,IAAI,SAAS,IAAI;YAC3C,KAAK;gBACH,OAAO,CAAC,KAAK,QAAQ,CAAC,WAAW,IAAI,SAAS,IAAI;YACpD,KAAK;gBACH,IAAI,YAAY,KAAK,MAAM;gBAC3B,OAAO,KAAK,WAAW;gBACvB,QACE,CAAC,AAAC,OAAO,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI,IACnD,OAAO,OAAO,OAAO,gBAAgB,OAAO,MAAM,YAAa;gBAClE,OAAO;YACT,KAAK;gBACH,OACE,AAAC,YAAY,KAAK,WAAW,IAAI,MACjC,SAAS,YACL,YACA,yBAAyB,KAAK,IAAI,KAAK;YAE/C,KAAK;gBACH,YAAY,KAAK,QAAQ;gBACzB,OAAO,KAAK,KAAK;gBACjB,IAAI;oBACF,OAAO,yBAAyB,KAAK;gBACvC,EAAE,OAAO,GAAG,CAAC;QACjB;QACF,OAAO;IACT;IACA,SAAS,mBAAmB,KAAK;QAC/B,OAAO,KAAK;IACd;IACA,SAAS,uBAAuB,KAAK;QACnC,IAAI;YACF,mBAAmB;YACnB,IAAI,2BAA2B,CAAC;QAClC,EAAE,OAAO,GAAG;YACV,2BAA2B,CAAC;QAC9B;QACA,IAAI,0BAA0B;YAC5B,2BAA2B;YAC3B,IAAI,wBAAwB,yBAAyB,KAAK;YAC1D,IAAI,oCACF,AAAC,eAAe,OAAO,UACrB,OAAO,WAAW,IAClB,KAAK,CAAC,OAAO,WAAW,CAAC,IAC3B,MAAM,WAAW,CAAC,IAAI,IACtB;YACF,sBAAsB,IAAI,CACxB,0BACA,4GACA;YAEF,OAAO,mBAAmB;QAC5B;IACF;IACA,SAAS,YAAY,IAAI;QACvB,IAAI,SAAS,qBAAqB,OAAO;QACzC,IACE,aAAa,OAAO,QACpB,SAAS,QACT,KAAK,QAAQ,KAAK,iBAElB,OAAO;QACT,IAAI;YACF,IAAI,OAAO,yBAAyB;YACpC,OAAO,OAAO,MAAM,OAAO,MAAM;QACnC,EAAE,OAAO,GAAG;YACV,OAAO;QACT;IACF;IACA,SAAS;QACP,IAAI,aAAa,qBAAqB,CAAC;QACvC,OAAO,SAAS,aAAa,OAAO,WAAW,QAAQ;IACzD;IACA,SAAS;QACP,OAAO,MAAM;IACf;IACA,SAAS,YAAY,MAAM;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,IAAI,SAAS,OAAO,wBAAwB,CAAC,QAAQ,OAAO,GAAG;YAC/D,IAAI,UAAU,OAAO,cAAc,EAAE,OAAO,CAAC;QAC/C;QACA,OAAO,KAAK,MAAM,OAAO,GAAG;IAC9B;IACA,SAAS,2BAA2B,KAAK,EAAE,WAAW;QACpD,SAAS;YACP,8BACE,CAAC,AAAC,6BAA6B,CAAC,GAChC,QAAQ,KAAK,CACX,2OACA,YACD;QACL;QACA,sBAAsB,cAAc,GAAG,CAAC;QACxC,OAAO,cAAc,CAAC,OAAO,OAAO;YAClC,KAAK;YACL,cAAc,CAAC;QACjB;IACF;IACA,SAAS;QACP,IAAI,gBAAgB,yBAAyB,IAAI,CAAC,IAAI;QACtD,sBAAsB,CAAC,cAAc,IACnC,CAAC,AAAC,sBAAsB,CAAC,cAAc,GAAG,CAAC,GAC3C,QAAQ,KAAK,CACX,8IACD;QACH,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG;QAC9B,OAAO,KAAK,MAAM,gBAAgB,gBAAgB;IACpD;IACA,SAAS,aACP,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,MAAM,EACN,KAAK,EACL,KAAK,EACL,UAAU,EACV,SAAS;QAET,OAAO,MAAM,GAAG;QAChB,OAAO;YACL,UAAU;YACV,MAAM;YACN,KAAK;YACL,OAAO;YACP,QAAQ;QACV;QACA,SAAS,CAAC,KAAK,MAAM,OAAO,OAAO,IAAI,IACnC,OAAO,cAAc,CAAC,MAAM,OAAO;YACjC,YAAY,CAAC;YACb,KAAK;QACP,KACA,OAAO,cAAc,CAAC,MAAM,OAAO;YAAE,YAAY,CAAC;YAAG,OAAO;QAAK;QACrE,KAAK,MAAM,GAAG,CAAC;QACf,OAAO,cAAc,CAAC,KAAK,MAAM,EAAE,aAAa;YAC9C,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,eAAe;YACzC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,KAAK,KAAK,GAAG,OAAO,MAAM,CAAC,KAAK;QAChE,OAAO;IACT;IACA,SAAS,WACP,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI,EACJ,UAAU,EACV,SAAS;QAET,IAAI,WAAW,OAAO,QAAQ;QAC9B,IAAI,KAAK,MAAM,UACb,IAAI,kBACF,IAAI,YAAY,WAAW;YACzB,IACE,mBAAmB,GACnB,mBAAmB,SAAS,MAAM,EAClC,mBAEA,kBAAkB,QAAQ,CAAC,iBAAiB;YAC9C,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC;QACjC,OACE,QAAQ,KAAK,CACX;aAED,kBAAkB;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,WAAW,yBAAyB;YACpC,IAAI,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,SAAU,CAAC;gBAC/C,OAAO,UAAU;YACnB;YACA,mBACE,IAAI,KAAK,MAAM,GACX,oBAAoB,KAAK,IAAI,CAAC,aAAa,WAC3C;YACN,qBAAqB,CAAC,WAAW,iBAAiB,IAChD,CAAC,AAAC,OACA,IAAI,KAAK,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,aAAa,WAAW,MAC5D,QAAQ,KAAK,CACX,mOACA,kBACA,UACA,MACA,WAED,qBAAqB,CAAC,WAAW,iBAAiB,GAAG,CAAC,CAAE;QAC7D;QACA,WAAW;QACX,KAAK,MAAM,YACT,CAAC,uBAAuB,WAAY,WAAW,KAAK,QAAS;QAC/D,YAAY,WACV,CAAC,uBAAuB,OAAO,GAAG,GAAI,WAAW,KAAK,OAAO,GAAG,AAAC;QACnE,IAAI,SAAS,QAAQ;YACnB,WAAW,CAAC;YACZ,IAAK,IAAI,YAAY,OACnB,UAAU,YAAY,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;QAChE,OAAO,WAAW;QAClB,YACE,2BACE,UACA,eAAe,OAAO,OAClB,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,YACjC;QAER,OAAO,aACL,MACA,UACA,MACA,QACA,YACA,UACA,YACA;IAEJ;IACA,SAAS,kBAAkB,IAAI;QAC7B,aAAa,OAAO,QAClB,SAAS,QACT,KAAK,QAAQ,KAAK,sBAClB,KAAK,MAAM,IACX,CAAC,KAAK,MAAM,CAAC,SAAS,GAAG,CAAC;IAC9B;IACA,IAAI,uHACF,qBAAqB,OAAO,GAAG,CAAC,+BAChC,oBAAoB,OAAO,GAAG,CAAC,iBAC/B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC;IACnC,OAAO,GAAG,CAAC;IACX,IAAI,sBAAsB,OAAO,GAAG,CAAC,mBACnC,qBAAqB,OAAO,GAAG,CAAC,kBAChC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,2BAA2B,OAAO,GAAG,CAAC,wBACtC,kBAAkB,OAAO,GAAG,CAAC,eAC7B,kBAAkB,OAAO,GAAG,CAAC,eAC7B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,2BACpC,uBACE,MAAM,+DAA+D,EACvE,iBAAiB,OAAO,SAAS,CAAC,cAAc,EAChD,cAAc,MAAM,OAAO,EAC3B,aAAa,QAAQ,UAAU,GAC3B,QAAQ,UAAU,GAClB;QACE,OAAO;IACT;IACN,QAAQ;QACN,4BAA4B,SAAU,iBAAiB;YACrD,OAAO;QACT;IACF;IACA,IAAI;IACJ,IAAI,yBAAyB,CAAC;IAC9B,IAAI,yBAAyB,KAAK,CAAC,2BAA2B,CAAC,IAAI,CACjE,OACA;IAEF,IAAI,wBAAwB,WAAW,YAAY;IACnD,IAAI,wBAAwB,CAAC;IAC7B,QAAQ,QAAQ,GAAG;IACnB,QAAQ,MAAM,GAAG,SACf,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI;QAEJ,IAAI,mBACF,MAAM,qBAAqB,0BAA0B;QACvD,OAAO,WACL,MACA,QACA,UACA,kBACA,QACA,MACA,mBACI,MAAM,2BACN,wBACJ,mBAAmB,WAAW,YAAY,SAAS;IAEvD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 972, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Community%20Tool%20Lending%20Platform/my-tool-lending-app/node_modules/next/dist/compiled/react/jsx-dev-runtime.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 984, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Projects/Community%20Tool%20Lending%20Platform/my-tool-lending-app/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}