#!/usr/bin/env node

/**
 * Database Connection Test Script
 * 
 * This script performs comprehensive testing of the MongoDB database connection
 * and operations for the Community Tool Lending Platform.
 */

import { config } from 'dotenv';
import path from 'path';
import DatabaseTester from '../lib/db-test';
import { disconnectMongoDB } from '../lib/mongodb';

// Load environment variables
config({ path: path.resolve(process.cwd(), '.env.local') });

async function main() {
  console.log("🔧 Community Tool Lending Platform - Database Test");
  console.log("=" * 60);
  console.log(`📅 Test started at: ${new Date().toISOString()}`);
  console.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`🔗 MongoDB URI: ${process.env.MONGODB_URI ? 'Configured ✅' : 'Missing ❌'}`);
  console.log("=" * 60);

  if (!process.env.MONGODB_URI) {
    console.error("❌ MONGODB_URI environment variable is not set!");
    console.error("Please check your .env.local file.");
    process.exit(1);
  }

  const tester = new DatabaseTester();
  
  try {
    // Run all database tests
    const results = await tester.runAllTests();
    
    // Print detailed summary
    tester.printSummary();
    
    // Check if all tests passed
    const allPassed = results.every(result => result.success);
    
    if (allPassed) {
      console.log("\n🎉 All database tests passed successfully!");
      console.log("✅ Database connection is working properly.");
      console.log("✅ All CRUD operations are functional.");
      console.log("✅ Database statistics are accessible.");
    } else {
      console.log("\n⚠️ Some database tests failed!");
      console.log("Please check the error details above.");
    }
    
    // Export results to JSON for further analysis
    const testReport = {
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development',
      mongoUri: process.env.MONGODB_URI ? 'configured' : 'missing',
      results: results,
      summary: {
        total: results.length,
        passed: results.filter(r => r.success).length,
        failed: results.filter(r => !r.success).length,
        allPassed
      }
    };
    
    console.log("\n📄 Test Report:");
    console.log(JSON.stringify(testReport, null, 2));
    
    // Exit with appropriate code
    process.exit(allPassed ? 0 : 1);
    
  } catch (error) {
    console.error("\n💥 Unexpected error during testing:");
    console.error(error);
    process.exit(1);
  } finally {
    // Always try to disconnect
    try {
      await disconnectMongoDB();
    } catch (error) {
      console.error("Error during cleanup:", error);
    }
  }
}

// Handle process termination gracefully
process.on('SIGINT', async () => {
  console.log('\n🛑 Test interrupted by user');
  try {
    await disconnectMongoDB();
  } catch (error) {
    console.error("Error during cleanup:", error);
  }
  process.exit(1);
});

process.on('SIGTERM', async () => {
  console.log('\n🛑 Test terminated');
  try {
    await disconnectMongoDB();
  } catch (error) {
    console.error("Error during cleanup:", error);
  }
  process.exit(1);
});

// Run the main function
if (require.main === module) {
  main().catch((error) => {
    console.error("Fatal error:", error);
    process.exit(1);
  });
}

export default main;
